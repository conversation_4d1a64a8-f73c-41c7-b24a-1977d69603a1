#!/usr/bin/env python3
"""
Test script to debug the document processor
"""
import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from zurich_ocr.core.document_processor import DocumentProcessor

async def test_document_processor():
    """Test the document processor with a simple text file"""
    
    processor = DocumentProcessor()
    
    # Test with sample text file
    with open('test_documents/sample.txt', 'rb') as f:
        file_data = f.read()
    
    try:
        result = await processor.process_document(
            file_data, 
            'sample.txt', 
            'test-request-123'
        )
        
        print("Document processing result:")
        print(f"Type: {result['type']}")
        print(f"Requires OCR: {result['requires_ocr']}")
        print(f"Text length: {len(result.get('text', ''))}")
        print(f"Text preview: {result.get('text', '')[:100]}")
        print(f"Metadata: {result.get('metadata', {})}")
        
        return result
        
    except Exception as e:
        print(f"Error in document processing: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = asyncio.run(test_document_processor())
