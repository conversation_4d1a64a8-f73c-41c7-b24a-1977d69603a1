#!/usr/bin/env python3
"""
🏆 Google Document AI Setup Script for Zurich OCR Engine
This script helps you create all necessary processors for the award-winning OCR solution
"""

import json
import os
from google.cloud import documentai
from google.oauth2 import service_account

def setup_document_ai_processors():
    """
    Setup all Document AI processors for the award-winning OCR engine
    """
    
    print("🏆 Setting up Google Document AI Processors for Zurich OCR Engine")
    print("=" * 70)
    
    # Configuration
    PROJECT_ID = input("Enter your Google Cloud Project ID: ").strip()
    LOCATION = input("Enter location (default: us): ").strip() or "us"
    
    # Load credentials
    credentials_path = input("Enter path to your service account JSON file: ").strip()
    
    if not os.path.exists(credentials_path):
        print(f"❌ Credentials file not found: {credentials_path}")
        return
    
    # Initialize client
    credentials = service_account.Credentials.from_service_account_file(credentials_path)
    client = documentai.DocumentProcessorServiceClient(credentials=credentials)
    
    # Parent path
    parent = f"projects/{PROJECT_ID}/locations/{LOCATION}"
    
    # Processors to create
    processors_to_create = [
        {
            "display_name": "zurich-general-ocr",
            "type": "OCR_PROCESSOR",
            "description": "General OCR for text extraction and handwriting recognition"
        },
        {
            "display_name": "zurich-form-parser", 
            "type": "FORM_PARSER_PROCESSOR",
            "description": "Form parser for insurance forms and applications"
        },
        {
            "display_name": "zurich-invoice-processor",
            "type": "INVOICE_PROCESSOR", 
            "description": "Invoice processor for premium and payment documents"
        },
        {
            "display_name": "zurich-expense-processor",
            "type": "EXPENSE_PROCESSOR",
            "description": "Expense processor for receipts and travel claims"
        },
        {
            "display_name": "zurich-custom-extractor",
            "type": "CUSTOM_EXTRACTION_PROCESSOR",
            "description": "Custom extractor for insurance-specific documents"
        }
    ]
    
    created_processors = {}
    
    print(f"\n🚀 Creating processors in project: {PROJECT_ID}")
    print(f"📍 Location: {LOCATION}")
    print("-" * 50)
    
    for processor_config in processors_to_create:
        try:
            print(f"Creating {processor_config['display_name']}...")
            
            # Create processor request
            processor = documentai.Processor(
                display_name=processor_config["display_name"],
                type_=processor_config["type"]
            )
            
            request = documentai.CreateProcessorRequest(
                parent=parent,
                processor=processor
            )
            
            # Create the processor
            operation = client.create_processor(request=request)
            result = operation.result()
            
            processor_id = result.name.split("/")[-1]
            created_processors[processor_config["display_name"]] = {
                "id": processor_id,
                "name": result.name,
                "type": processor_config["type"],
                "display_name": result.display_name
            }
            
            print(f"✅ Created: {processor_config['display_name']} (ID: {processor_id})")
            
        except Exception as e:
            print(f"❌ Failed to create {processor_config['display_name']}: {str(e)}")
    
    # Generate configuration
    print("\n" + "=" * 70)
    print("🎯 CONFIGURATION FOR YOUR .env FILE")
    print("=" * 70)
    
    print(f"GOOGLE_PROJECT_ID={PROJECT_ID}")
    print(f"GOOGLE_LOCATION={LOCATION}")
    
    # Read and display credentials JSON
    with open(credentials_path, 'r') as f:
        credentials_json = json.load(f)
    
    # Escape the JSON for .env file
    credentials_str = json.dumps(credentials_json).replace('"', '\\"')
    print(f'GOOGLE_CREDENTIALS_JSON="{credentials_str}"')
    
    print("\n" + "=" * 70)
    print("📋 CREATED PROCESSORS")
    print("=" * 70)
    
    for name, info in created_processors.items():
        print(f"• {name}")
        print(f"  Type: {info['type']}")
        print(f"  ID: {info['id']}")
        print(f"  Full Name: {info['name']}")
        print()
    
    print("🏆 Setup Complete!")
    print("Copy the configuration above to your .env file to enable award-winning OCR!")
    
    return created_processors

if __name__ == "__main__":
    try:
        setup_document_ai_processors()
    except KeyboardInterrupt:
        print("\n❌ Setup cancelled by user")
    except Exception as e:
        print(f"\n❌ Setup failed: {str(e)}")
        print("Please check your credentials and project configuration.")
