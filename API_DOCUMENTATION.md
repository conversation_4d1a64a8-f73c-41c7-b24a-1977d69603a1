# Zurich OCR Engine - API Documentation

## 🏆 Advanced OCR Solution with LLM-Powered Intelligent Routing

**Version:** 2.0.0
**Base URL:** `http://localhost:8000` (Development) | `https://zurich-ocr.dev-scc-demo.rozie.ai` (Production)
**Documentation:** [Interactive Docs](http://localhost:8000/docs) | [ReDoc](http://localhost:8000/redoc)

---

## 📋 Table of Contents

1. [Overview](#overview)
2. [Authentication](#authentication)
3. [Health & Status Endpoints](#health--status-endpoints)
4. [OCR Processing Endpoints](#ocr-processing-endpoints)
5. [Configuration Endpoints](#configuration-endpoints)
6. [Utility Endpoints](#utility-endpoints)
7. [Request/Response Models](#requestresponse-models)
8. [Configuration Examples](#configuration-examples)
9. [Error Handling](#error-handling)
10. [Rate Limits & Performance](#rate-limits--performance)
11. [Integration Examples](#integration-examples)
12. [Advanced Features](#advanced-features)

---

## 🔍 Overview

The Zurich OCR Engine is an enterprise-grade OCR solution featuring:

- **🤖 GPT-4 Powered Intelligent Routing**: Automatically selects optimal OCR processors
- **🔧 Google Document AI Integration**: 4 specialized processors for different document types
- **📄 Universal Format Support**: 44+ file formats including images, PDFs, Office documents, archives
- **⚡ High-Performance Processing**: Parallel processing with 50% speed optimization (V2)
- **🏥 Insurance Industry Optimization**: Specialized for insurance documents and workflows
- **🔄 Advanced Fallback Strategies**: Multiple OCR engines with intelligent failover
- **🖼️ Advanced Preprocessing**: AI-controlled image enhancement for better OCR accuracy
- **🧠 GPT Post-Processing**: V1 (full-featured) and V2 (speed-optimized) text enhancement

### Key Features

| Feature | Description |
|---------|-------------|
| **Intelligent Routing** | LLM-powered document analysis for optimal processor selection |
| **Multi-Engine Support** | Google Document AI, Tesseract OCR, with smart fallbacks |
| **Format Detection** | Automatic file format detection and appropriate processing |
| **Post-Processing V1/V2** | GPT-powered text enhancement (V1: full-featured, V2: 50% faster) |
| **Advanced Preprocessing** | AI-controlled image enhancement with 10+ techniques |
| **Parallel Processing** | High-throughput concurrent document processing (8 workers) |
| **Quality Assessment** | Confidence scoring and detailed quality metrics |
| **Structured Data Extraction** | Entities, tables, form fields with confidence scores |
| **Debug Mode** | Comprehensive step-by-step processing information |
| **Insurance Optimization** | Specialized processing for insurance industry documents |

---

## 🔐 Authentication

Currently, the API uses environment-based authentication. API keys are configured server-side for:
- **OpenAI API**: For LLM routing and post-processing
- **Google Cloud**: For Document AI processors
- **AWS/Azure**: For optional cloud integrations

*Note: Client authentication will be added in future versions.*

---

## 🏥 Health & Status Endpoints

### 1. Readiness Check

**Endpoint:** `GET /readiness`
**Purpose:** AWS ALB health check endpoint

```bash
curl -X GET "http://localhost:8000/readiness"
```

**Response:**
```json
{
  "service": "zurich-ocr-engine",
  "status": "ready",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "2.0.0",
  "llm_configured": true
}
```

### 2. Comprehensive Health Check

**Endpoint:** `GET /api/v1/health`
**Purpose:** Detailed system component status

```bash
curl -X GET "http://localhost:8000/api/v1/health"
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "2.0.0",
  "components": {
    "llm_router": {
      "status": "healthy",
      "statistics": {
        "total_requests": 1250,
        "success_rate": 0.98,
        "average_response_time": 1.2
      }
    },
    "google_ai_hub": {
      "status": "healthy",
      "processor_status": {
        "available_processors": 12,
        "active_processors": 8
      }
    },
    "universal_format_engine": {
      "status": "healthy",
      "supported_formats": 44
    },
    "settings": {
      "llm_routing_enabled": true,
      "parallel_processing": true,
      "debug_mode": false
    }
  }
}
```

---

## 🔧 OCR Processing Endpoints

### 1. Text Extraction (Primary Endpoint)

**Endpoint:** `POST /api/v1/extract-text`  
**Purpose:** Extract text from documents with intelligent routing

**Content-Type:** `multipart/form-data`

**Parameters:**
- `file` (required): Document file to process
- `config` (optional): JSON configuration string

```bash
curl -X POST "http://localhost:8000/api/v1/extract-text" \
  -F "file=@document.pdf" \
  -F 'config={"llm_routing_enabled": true, "post_processing": "v2", "preprocessing": "auto"}'
```

**Configuration Options:**
```json
{
  // Core Routing Configuration
  "llm_routing_enabled": true,           // Enable GPT-4 intelligent routing
  "parallel_processing": true,           // Enable concurrent processing
  "confidence_threshold": 0.85,          // Minimum confidence (0.0-1.0)
  "enable_fallbacks": true,              // Enable fallback strategies
  "debug_mode": false,                   // Enable detailed debug info
  "insurance_optimization": true,        // Insurance industry optimizations

  // Direct OCR Engine Selection (bypasses LLM routing)
  "ocr_engine": "google",                // Options: "google", "tesseract"
  "google_processor": "OCR_PROCESSOR",   // Google processor type
  "tesseract_psm": 6,                    // Tesseract PSM mode (0-13)

  // Post-Processing Configuration
  "post_processing": "v2",               // Options: "v1", "v2", false

  // Preprocessing Configuration
  "preprocessing": "auto",               // Options: "auto", "minimal", "basic", "enhanced", "aggressive", false
  "preprocessing_force_level": null,     // Override AI preprocessing decision
  "preprocessing_techniques": {          // Fine-grained control
    "techniques": ["grayscale", "denoise", "deskew"]
  }
}
```

**Response:**
```json
{
  "success": true,
  "extracted_text": "Sample extracted text content...",
  "confidence": 0.95,
  "document_type": "invoice",
  "processor_used": "INVOICE_PROCESSOR_+_gpt_postprocessing",
  "processing_time_ms": 2450.5,
  "entities": [
    {
      "type": "amount",
      "value": "$1,234.56",
      "confidence": 0.98
    }
  ],
  "tables": [
    {
      "rows": 5,
      "columns": 3,
      "data": [[...]]
    }
  ],
  "form_fields": [
    {
      "name": "invoice_number",
      "value": "INV-2024-001",
      "confidence": 0.97
    }
  ],
  "metadata": {
    "request_id": "req_abc123",
    "filename": "invoice.pdf",
    "file_size": 245760,
    "text_length": 1250,
    "timestamp": "2024-01-15T10:30:00Z",
    "processing_config": {...},
    "preprocessing_info": {
      "applied": true,
      "techniques": ["deskew", "noise_reduction"],
      "quality_improvement": 0.15,
      "processing_time": 0.8
    }
  },
  "format_info": {
    "format_type": "pdf",
    "extension": "pdf",
    "mime_type": "application/pdf",
    "page_count": 2
  },
  "document_analysis": {
    "document_type": "invoice",
    "complexity": "medium",
    "language": "en",
    "confidence": 0.92
  },
  "processing_strategy": {
    "primary_processor": "INVOICE_PROCESSOR",
    "fallback_processors": ["OCR_PROCESSOR"],
    "reasoning": "Document identified as invoice with structured data"
  },
  "postprocessed_data": {
    "structured_data": {...},
    "original_text": "...",
    "enhanced_text": "..."
  },
  "step_timings": {
    "1_format_detection_and_extraction": 150.2,
    "1.5_preprocessing": 800.5,
    "2_llm_routing_analysis": 1200.8,
    "3_ocr_processing": 2100.3,
    "4_gpt_postprocessing": 1500.7
  }
}
```

### 2. Processing Pipeline Details

The OCR processing follows these steps:

1. **Format Detection**: Automatic file type identification
2. **Preprocessing** (optional): AI-controlled image enhancement
3. **LLM Analysis** (optional): Document type and complexity analysis
4. **OCR Processing**: Google Document AI or Tesseract with intelligent routing
5. **Post-Processing** (optional): GPT-powered text cleaning and structuring
6. **Response Generation**: Comprehensive results with metadata

**Processing Modes:**

| Mode | Description | Use Case | Speed |
|------|-------------|----------|-------|
| **Speed Optimized (V2)** | 50% faster processing | High-volume, real-time | Fast |
| **Quality Optimized (V1)** | Maximum accuracy | Complex documents, critical accuracy | Standard |
| **Direct OCR** | Bypass LLM routing | Known document types, cost optimization | Very Fast |
| **Minimal Processing** | Raw OCR only | Simple text extraction, testing | Fastest |

---

## ⚙️ Configuration Endpoints

### 1. API Configuration

**Endpoint:** `GET /api/v1/config`
**Purpose:** Get comprehensive API configuration and capabilities

```bash
curl -X GET "http://localhost:8000/api/v1/config"
```

**Response:**
```json
{
  "supported_formats": [
    "pdf", "png", "jpg", "jpeg", "gif", "bmp", "tiff", "webp", "svg", "ico",
    "docx", "doc", "xlsx", "xls", "pptx", "ppt", "pptm", "txt", "csv", "html", "htm", "xml", "mhtml",
    "json", "yaml", "yml", "rtf", "zip", "rar", "7z", "tar", "gz", "bz2",
    "msg", "eml", "pbix", "pbit", "eps", "ps", "ai", "odt", "ods", "odp"
  ],
  "processors": {
    "google_document_ai": {
      "available": true,
      "processors": {
        "OCR_PROCESSOR": "General OCR processing",
        "FORM_PARSER_PROCESSOR": "Form and table extraction",
        "INVOICE_PROCESSOR": "Invoice processing",
        "LAYOUT_PARSER_PROCESSOR": "Layout analysis and complex documents"
      },
      "capabilities": {
        "OCR_PROCESSOR": {
          "strengths": ["general_text", "handwriting", "multi_language"],
          "accuracy": 0.95,
          "speed": "fast",
          "max_pages": 15
        }
      }
    },
    "llm_router": {
      "available": true,
      "model": "gpt-4o",
      "enabled": true
    }
  },
  "features": {
    "llm_intelligent_routing": true,
    "google_document_ai": true,
    "universal_format_support": true,
    "parallel_processing": true,
    "insurance_optimization": true,
    "batch_processing": true,
    "multi_language_support": true,
    "table_extraction": true,
    "form_processing": true,
    "entity_extraction": true
  },
  "settings": {
    "max_file_size": ********,
    "max_workers": 8,
    "confidence_threshold": 0.85,
    "batch_size": 10,
    "cache_enabled": true,
    "debug_mode": false,
    "preprocessing_enabled": true,
    "post_processing_v1": true,
    "post_processing_v2": true,
    "insurance_optimization": true
  }
}
```

### 2. Supported File Formats

**Endpoint:** `GET /api/v1/supported-formats`
**Purpose:** Get detailed information about supported file formats

```bash
curl -X GET "http://localhost:8000/api/v1/supported-formats"
```

**Response:**
```json
{
  "total_formats": 44,
  "supported_formats": [
    "pdf", "png", "jpg", "jpeg", "gif", "bmp", "tiff", "webp", "svg", "ico",
    "docx", "doc", "xlsx", "xls", "pptx", "ppt", "pptm", "txt", "csv", "html", "htm", "xml", "mhtml",
    "json", "yaml", "yml", "rtf", "zip", "rar", "7z", "tar", "gz", "bz2",
    "msg", "eml", "pbix", "pbit", "eps", "ps", "ai", "odt", "ods", "odp"
  ],
  "categories": {
    "images": ["png", "jpg", "jpeg", "gif", "bmp", "tiff", "webp", "svg", "ico"],
    "documents": ["pdf"],
    "office": ["docx", "doc", "xlsx", "xls", "pptx", "ppt", "pptm"],
    "text": ["txt", "csv", "json", "yaml", "yml", "rtf"],
    "web": ["html", "htm", "xml", "mhtml"],
    "email": ["msg", "eml"],
    "archives": ["zip", "rar", "7z", "tar", "gz", "bz2"],
    "business_intelligence": ["pbix", "pbit"],
    "open_office": ["odt", "ods", "odp"],
    "graphics": ["eps", "ps", "ai"]
  },
  "processing_methods": {
    "direct_extraction": ["txt", "csv", "docx", "xlsx", "pptx", "html"],
    "ocr_required": ["png", "jpg", "jpeg", "gif", "bmp", "tiff", "webp"],
    "hybrid_processing": ["pdf"],
    "archive_extraction": ["zip", "7z"]
  },
  "google_ai_processors": {
    "OCR_PROCESSOR": "General text extraction",
    "FORM_PARSER_PROCESSOR": "Forms and tables",
    "INVOICE_PROCESSOR": "Invoices and bills",
    "LAYOUT_PARSER_PROCESSOR": "Layout analysis and complex documents"
  }
}
```

### 3. Configuration Examples

**Endpoint:** `GET /api/v1/configuration-examples`
**Purpose:** Get pre-configured examples for different use cases

```bash
curl -X GET "http://localhost:8000/api/v1/configuration-examples"
```

**Response:**
```json
{
  "examples": {
    "speed_optimized": {
      "config": {
        "llm_routing_enabled": true,
        "post_processing": "v2",
        "preprocessing": "auto",
        "debug_mode": false
      },
      "description": "Optimized for fastest processing with good quality",
      "use_cases": ["High-volume processing", "Real-time applications"],
      "expected_speed": "50% faster than quality-optimized"
    },
    "quality_optimized": {
      "config": {
        "llm_routing_enabled": true,
        "post_processing": "v1",
        "preprocessing": "enhanced",
        "debug_mode": true
      },
      "description": "Maximum accuracy with comprehensive analysis",
      "use_cases": ["Complex documents", "Legal documents"]
    },
    "insurance_claims": {
      "config": {
        "llm_routing_enabled": true,
        "post_processing": "v1",
        "preprocessing": "enhanced",
        "insurance_optimization": true,
        "debug_mode": true
      },
      "description": "Optimized for insurance claim documents"
    }
  },
  "configuration_options": {
    "ocr_engines": ["google", "tesseract"],
    "google_processors": ["OCR_PROCESSOR", "FORM_PARSER_PROCESSOR", "INVOICE_PROCESSOR", "LAYOUT_PARSER_PROCESSOR"],
    "post_processing_versions": ["v1", "v2", "false"],
    "preprocessing_levels": ["auto", "minimal", "basic", "enhanced", "aggressive", "false"]
  }
}
```

### 4. Configuration Schema

**Endpoint:** `GET /api/v1/configuration-schema`
**Purpose:** Get complete configuration schema with validation rules

```bash
curl -X GET "http://localhost:8000/api/v1/configuration-schema"
```

**Response:**
```json
{
  "title": "OCR Processing Configuration Schema",
  "version": "2.0.0",
  "schema": {
    "type": "object",
    "properties": {
      "llm_routing_enabled": {
        "type": "boolean",
        "default": true,
        "description": "Enable LLM-powered intelligent routing"
      },
      "post_processing": {
        "anyOf": [
          {"type": "string", "enum": ["v1", "v2"]},
          {"type": "boolean"}
        ],
        "default": "v1",
        "description": "Post-processing version or disable"
      }
    }
  },
  "validation_rules": {
    "confidence_threshold": "Must be between 0.0 and 1.0",
    "tesseract_psm": "Must be between 0 and 13",
    "ocr_engine": "Must be 'google' or 'tesseract'"
  }
}
```

### 5. Request/Response Examples

**Endpoint:** `GET /api/v1/request-response-examples`
**Purpose:** Get comprehensive request and response examples

```bash
curl -X GET "http://localhost:8000/api/v1/request-response-examples"
```

**Response includes:**
- Complete request structures (multipart form data)
- Full response objects with all fields
- cURL and JavaScript examples
- Error response examples
```

### 3. Processing Statistics

**Endpoint:** `GET /api/v1/stats`
**Purpose:** Get processing statistics and performance metrics

```bash
curl -X GET "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/stats"
```

**Response:**
```json
{
  "routing_statistics": {
    "total_requests": 1250,
    "successful_routes": 1225,
    "success_rate": 0.98,
    "average_routing_time": 1.2,
    "processor_usage": {
      "OCR_PROCESSOR": 450,
      "INVOICE_PROCESSOR": 320,
      "FORM_PARSER_PROCESSOR": 280,
      "EXPENSE_PROCESSOR": 175
    }
  },
  "processor_status": {
    "google_document_ai": {
      "status": "healthy",
      "available_processors": 12,
      "active_processors": 8,
      "total_processed": 1180
    }
  },
  "system_info": {
    "version": "2.0.0",
    "uptime": "72h 15m",
    "supported_formats": 32,
    "available_processors": 12
  },
  "performance_metrics": {
    "average_processing_time": "2.4s",
    "success_rate": "98.2%",
    "cache_hit_rate": "15.3%"
  }
}
```

---

## 🛠️ Utility Endpoints

### 1. Clear Cache

**Endpoint:** `POST /api/v1/clear-cache`
**Purpose:** Clear processing cache to free memory

```bash
curl -X POST "http://localhost:8000/api/v1/clear-cache"
```

**Response:**
```json
{
  "success": true,
  "message": "Cache cleared successfully",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

---

## 🎯 Configuration Examples

### Speed Optimized Configuration (50% Faster)

**Use Case:** High-volume processing, real-time applications, simple documents

```json
{
  "llm_routing_enabled": true,
  "post_processing": "v2",
  "preprocessing": "auto",
  "debug_mode": false,
  "confidence_threshold": 0.85
}
```

**Benefits:**
- 50% faster processing than quality-optimized
- Good accuracy for most document types
- Reduced API costs
- Suitable for batch processing

### Quality Optimized Configuration (Maximum Accuracy)

**Use Case:** Complex documents, legal documents, critical accuracy requirements

```json
{
  "llm_routing_enabled": true,
  "post_processing": "v1",
  "preprocessing": "enhanced",
  "debug_mode": true,
  "confidence_threshold": 0.9
}
```

**Benefits:**
- Maximum accuracy and detail
- Comprehensive analysis and structuring
- Detailed debug information
- Best for complex layouts

### Direct OCR Configuration (No LLM Overhead)

**Use Case:** Known document types, consistent formats, cost optimization

```json
{
  "llm_routing_enabled": false,
  "ocr_engine": "google",
  "google_processor": "LAYOUT_PARSER_PROCESSOR",
  "post_processing": "v2",
  "preprocessing": "basic"
}
```

**Benefits:**
- No LLM analysis costs
- Fast processing
- Predictable results
- Good for known document formats

### Insurance Claims Specialized

**Use Case:** Insurance industry documents, claims processing

```json
{
  "llm_routing_enabled": true,
  "post_processing": "v1",
  "preprocessing": "enhanced",
  "insurance_optimization": true,
  "confidence_threshold": 0.9,
  "debug_mode": true
}
```

**Benefits:**
- Optimized for insurance documents
- Enhanced field extraction
- Industry-specific processing
- Detailed analysis

### Minimal Processing (Fastest)

**Use Case:** Simple text extraction, testing, high-speed requirements

```json
{
  "llm_routing_enabled": false,
  "ocr_engine": "google",
  "post_processing": false,
  "preprocessing": false,
  "debug_mode": false
}
```

**Benefits:**
- Fastest possible processing
- Minimal resource usage
- Raw OCR output
- Cost-effective

---

## 📊 Request/Response Models

### Processing Configuration Model

```json
{
  "llm_routing_enabled": true,
  "parallel_processing": true,
  "confidence_threshold": 0.85,
  "enable_fallbacks": true,
  "debug_mode": false,
  "insurance_optimization": true,
  "ocr_engine": "google",
  "google_processor": "OCR_PROCESSOR",
  "tesseract_psm": 6,
  "post_processing": "v1",
  "preprocessing": "auto",
  "preprocessing_force_level": null,
  "preprocessing_techniques": null
}
```

### Error Response Model

```json
{
  "success": false,
  "error": "File too large. Maximum size: ******** bytes",
  "status_code": 413,
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_error_123"
}
```

---

## ⚠️ Error Handling

### HTTP Status Codes

| Code | Description | Example |
|------|-------------|---------|
| `200` | Success | Successful processing |
| `400` | Bad Request | Invalid file format or configuration |
| `413` | Payload Too Large | File exceeds 50MB limit |
| `422` | Unprocessable Entity | Invalid JSON configuration |
| `500` | Internal Server Error | Processing failure |
| `503` | Service Unavailable | System overloaded or maintenance |

### Common Error Scenarios

1. **File Too Large**
   ```json
   {
     "success": false,
     "error": "File too large. Maximum size: ******** bytes",
     "status_code": 413
   }
   ```

2. **Unsupported Format**
   ```json
   {
     "success": false,
     "error": "Unsupported file format: .xyz",
     "status_code": 400
   }
   ```

3. **Processing Failure**
   ```json
   {
     "success": false,
     "error": "OCR processing failed: Unable to extract text",
     "status_code": 500
   }
   ```

---

## ⚡ Rate Limits & Performance

### Limits

| Parameter | Limit | Description |
|-----------|-------|-------------|
| **File Size** | 50MB | Maximum file size per request |
| **Batch Size** | 10 files | Maximum files per batch request |
| **Concurrent Requests** | 8 workers | Parallel processing capacity |
| **Request Timeout** | 5 minutes | Maximum processing time |

### Performance Optimization

- **Preprocessing**: Automatic image enhancement for better OCR accuracy
- **Intelligent Routing**: LLM selects optimal processor for each document type
- **Parallel Processing**: Multiple documents processed simultaneously
- **Caching**: Intelligent caching for repeated requests
- **Fallback Strategies**: Multiple OCR engines for reliability

---

## � Integration Examples

### JavaScript/TypeScript Integration

```javascript
// Using Fetch API
async function extractText(file, config = {}) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('config', JSON.stringify(config));

  const response = await fetch('/api/v1/extract-text', {
    method: 'POST',
    body: formData
  });

  const result = await response.json();
  return result;
}

// Example usage
const fileInput = document.getElementById('fileInput');
const config = {
  llm_routing_enabled: true,
  post_processing: "v2",
  preprocessing: "auto"
};

const result = await extractText(fileInput.files[0], config);
console.log('Extracted text:', result.extracted_text);
console.log('Confidence:', result.confidence);
```

### Python Integration

```python
import requests
import json

def extract_text(file_path, config=None):
    url = "http://localhost:8000/api/v1/extract-text"

    files = {'file': open(file_path, 'rb')}
    data = {}

    if config:
        data['config'] = json.dumps(config)

    response = requests.post(url, files=files, data=data)
    return response.json()

# Example usage
config = {
    "llm_routing_enabled": True,
    "post_processing": "v2",
    "preprocessing": "auto",
    "debug_mode": True
}

result = extract_text("document.pdf", config)
print(f"Success: {result['success']}")
print(f"Confidence: {result['confidence']}")
print(f"Text: {result['extracted_text'][:200]}...")
```

### Node.js Integration

```javascript
const FormData = require('form-data');
const fs = require('fs');
const axios = require('axios');

async function extractText(filePath, config = {}) {
  const form = new FormData();
  form.append('file', fs.createReadStream(filePath));
  form.append('config', JSON.stringify(config));

  try {
    const response = await axios.post(
      'http://localhost:8000/api/v1/extract-text',
      form,
      {
        headers: {
          ...form.getHeaders(),
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
    throw error;
  }
}

// Example usage
const config = {
  llm_routing_enabled: true,
  post_processing: "v2",
  insurance_optimization: true
};

extractText('./insurance_claim.pdf', config)
  .then(result => {
    console.log('Processing successful:', result.success);
    console.log('Document type:', result.document_type);
    console.log('Processor used:', result.processor_used);
  })
  .catch(error => {
    console.error('Processing failed:', error);
  });
```

---

## 🚀 Advanced Features

### Preprocessing Techniques

The system supports 10+ preprocessing techniques:

| Technique | Description | Use Case |
|-----------|-------------|----------|
| **grayscale** | Convert to grayscale | Reduce noise, improve OCR |
| **denoise** | Remove image noise | Poor quality scans |
| **deskew** | Correct document rotation | Skewed documents |
| **enhance_contrast** | Improve contrast | Low contrast images |
| **sharpen** | Sharpen blurry text | Blurry documents |
| **binarize** | Convert to black/white | High contrast OCR |
| **morphological** | Morphological operations | Clean up text |
| **remove_lines** | Remove table lines | Table processing |
| **remove_borders** | Remove document borders | Clean extraction |
| **resize** | Resize for optimal OCR | Size optimization |

### Post-Processing Versions

| Version | Features | Speed | Use Case |
|---------|----------|-------|----------|
| **V1** | Full-featured processing | Standard | Complex documents, maximum accuracy |
| **V2** | Speed-optimized | 50% faster | High-volume, real-time processing |

**V1 Features:**
- Content region analysis
- Drawing detection and metadata
- Comprehensive text cleaning
- Advanced field extraction
- Detailed confidence scoring

**V2 Features:**
- Parallel processing optimization
- Smart content truncation
- Streamlined text cleaning
- Fast field extraction
- Optimized confidence scoring

### Debug Mode

Enable comprehensive debugging with `"debug_mode": true`:

```json
{
  "debug_info": {
    "step_timings": {
      "1_format_detection_and_extraction": 125.3,
      "1.5_preprocessing": 245.2,
      "2_llm_routing_analysis": 1850.7,
      "3_ocr_processing": 8420.5,
      "4_gpt_postprocessing": 4905.2
    },
    "processing_decisions": {
      "routing_reason": "Complex layout with forms detected",
      "preprocessing_level_chosen": "enhanced",
      "fallback_triggered": false
    },
    "performance_metrics": {
      "total_api_calls": 3,
      "total_tokens_used": 2450,
      "cache_hits": 1
    }
  }
}
```

---

## �📝 Examples

### Basic Text Extraction

```bash
# Simple image OCR
curl -X POST "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/extract-text" \
  -F "file=@receipt.jpg"

# PDF with custom configuration
curl -X POST "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/extract-text" \
  -F "file=@invoice.pdf" \
  -F 'config={"post_processing": "v2", "confidence_threshold": 0.9}'
```

### Advanced Processing

```bash
# Insurance document with optimization
curl -X POST "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/extract-text" \
  -F "file=@insurance_claim.pdf" \
  -F 'config={
    "insurance_optimization": true,
    "post_processing": "v1",
    "preprocessing": "enhanced",
    "debug_mode": true
  }'

# Batch processing multiple files
curl -X POST "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/batch-process" \
  -F "files=@doc1.pdf" \
  -F "files=@doc2.png" \
  -F "files=@doc3.jpg" \
  -F 'config={"parallel_processing": true}'
```

### Health Monitoring

```bash
# Quick readiness check
curl "https://zurich-ocr.dev-scc-demo.rozie.ai/readiness"

# Detailed health status
curl "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/health"

# System statistics
curl "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/stats"
```

---

## 🔗 Additional Resources

- **Interactive API Documentation**: [Swagger UI](https://zurich-ocr.dev-scc-demo.rozie.ai/docs)
- **Alternative Documentation**: [ReDoc](https://zurich-ocr.dev-scc-demo.rozie.ai/redoc)
- **Health Dashboard**: [Health Check](https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/health)
- **Configuration**: [API Config](https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/config)

---

**© 2024 Zurich OCR Engine - Advanced OCR Solution with LLM-Powered Intelligent Routing**
```
