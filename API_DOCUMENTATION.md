# Zurich OCR Engine - API Documentation

## 🏆 Advanced OCR Solution with LLM-Powered Intelligent Routing

**Version:** 2.0.0  
**Base URL:** `https://zurich-ocr.dev-scc-demo.rozie.ai`  
**Documentation:** [Interactive Docs](https://zurich-ocr.dev-scc-demo.rozie.ai/docs) | [ReDoc](https://zurich-ocr.dev-scc-demo.rozie.ai/redoc)

---

## 📋 Table of Contents

1. [Overview](#overview)
2. [Authentication](#authentication)
3. [Health & Status Endpoints](#health--status-endpoints)
4. [OCR Processing Endpoints](#ocr-processing-endpoints)
5. [Configuration Endpoints](#configuration-endpoints)
6. [Utility Endpoints](#utility-endpoints)
7. [Request/Response Models](#requestresponse-models)
8. [Error Handling](#error-handling)
9. [Rate Limits & Performance](#rate-limits--performance)
10. [Examples](#examples)

---

## 🔍 Overview

The Zurich OCR Engine is an award-winning, enterprise-grade OCR solution featuring:

- **🤖 GPT-4 Powered Intelligent Routing**: Automatically selects optimal OCR processors
- **🔧 Google Document AI Integration**: 40+ specialized processors for different document types
- **📄 Universal Format Support**: 30+ file formats including images, PDFs, Office documents
- **⚡ High-Performance Processing**: Parallel processing with advanced optimization
- **🏥 Insurance Industry Optimization**: Specialized for insurance documents and workflows
- **🔄 Advanced Fallback Strategies**: Multiple OCR engines with intelligent failover

### Key Features

| Feature | Description |
|---------|-------------|
| **Intelligent Routing** | LLM-powered document analysis for optimal processor selection |
| **Multi-Engine Support** | Google Document AI, Tesseract OCR, with smart fallbacks |
| **Format Detection** | Automatic file format detection and appropriate processing |
| **Post-Processing** | GPT-powered text enhancement and structuring |
| **Batch Processing** | High-throughput parallel document processing |
| **Quality Assessment** | Confidence scoring and quality metrics |

---

## 🔐 Authentication

Currently, the API uses environment-based authentication. API keys are configured server-side for:
- **OpenAI API**: For LLM routing and post-processing
- **Google Cloud**: For Document AI processors
- **AWS/Azure**: For optional cloud integrations

*Note: Client authentication will be added in future versions.*

---

## 🏥 Health & Status Endpoints

### 1. Readiness Check

**Endpoint:** `GET /readiness`  
**Purpose:** AWS ALB health check endpoint

```bash
curl -X GET "https://zurich-ocr.dev-scc-demo.rozie.ai/readiness"
```

**Response:**
```json
{
  "service": "zurich-ocr-engine",
  "status": "ready",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "2.0.0",
  "llm_configured": true
}
```

### 2. Comprehensive Health Check

**Endpoint:** `GET /api/v1/health`  
**Purpose:** Detailed system component status

```bash
curl -X GET "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/health"
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "2.0.0",
  "components": {
    "llm_router": {
      "status": "healthy",
      "statistics": {
        "total_requests": 1250,
        "success_rate": 0.98,
        "average_response_time": 1.2
      }
    },
    "google_ai_hub": {
      "status": "healthy",
      "processor_status": {
        "available_processors": 12,
        "active_processors": 8
      }
    },
    "universal_format_engine": {
      "status": "healthy",
      "supported_formats": 32
    },
    "settings": {
      "llm_routing_enabled": true,
      "parallel_processing": true,
      "debug_mode": false
    }
  }
}
```

---

## 🔧 OCR Processing Endpoints

### 1. Text Extraction (Primary Endpoint)

**Endpoint:** `POST /api/v1/extract-text`  
**Purpose:** Extract text from documents with intelligent routing

**Content-Type:** `multipart/form-data`

**Parameters:**
- `file` (required): Document file to process
- `config` (optional): JSON configuration string

```bash
curl -X POST "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/extract-text" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.pdf" \
  -F 'config={"llm_routing_enabled": true, "post_processing": "v1", "confidence_threshold": 0.85}'
```

**Configuration Options:**
```json
{
  "llm_routing_enabled": true,
  "parallel_processing": true,
  "confidence_threshold": 0.85,
  "enable_fallbacks": true,
  "debug_mode": false,
  "insurance_optimization": true,
  "ocr_engine": "google",
  "google_processor": "OCR_PROCESSOR",
  "tesseract_psm": 6,
  "post_processing": "v1",
  "preprocessing": "auto",
  "preprocessing_force_level": null,
  "preprocessing_techniques": null
}
```

**Response:**
```json
{
  "success": true,
  "extracted_text": "Sample extracted text content...",
  "confidence": 0.95,
  "document_type": "invoice",
  "processor_used": "INVOICE_PROCESSOR_+_gpt_postprocessing",
  "processing_time_ms": 2450.5,
  "entities": [
    {
      "type": "amount",
      "value": "$1,234.56",
      "confidence": 0.98
    }
  ],
  "tables": [
    {
      "rows": 5,
      "columns": 3,
      "data": [[...]]
    }
  ],
  "form_fields": [
    {
      "name": "invoice_number",
      "value": "INV-2024-001",
      "confidence": 0.97
    }
  ],
  "metadata": {
    "request_id": "req_abc123",
    "filename": "invoice.pdf",
    "file_size": 245760,
    "text_length": 1250,
    "timestamp": "2024-01-15T10:30:00Z",
    "processing_config": {...},
    "preprocessing_info": {
      "applied": true,
      "techniques": ["deskew", "noise_reduction"],
      "quality_improvement": 0.15,
      "processing_time": 0.8
    }
  },
  "format_info": {
    "format_type": "pdf",
    "extension": "pdf",
    "mime_type": "application/pdf",
    "page_count": 2
  },
  "document_analysis": {
    "document_type": "invoice",
    "complexity": "medium",
    "language": "en",
    "confidence": 0.92
  },
  "processing_strategy": {
    "primary_processor": "INVOICE_PROCESSOR",
    "fallback_processors": ["OCR_PROCESSOR"],
    "reasoning": "Document identified as invoice with structured data"
  },
  "postprocessed_data": {
    "structured_data": {...},
    "original_text": "...",
    "enhanced_text": "..."
  },
  "step_timings": {
    "1_format_detection_and_extraction": 150.2,
    "1.5_preprocessing": 800.5,
    "2_llm_routing_analysis": 1200.8,
    "3_ocr_processing": 2100.3,
    "4_gpt_postprocessing": 1500.7
  }
}
```

### 2. Batch Processing

**Endpoint:** `POST /api/v1/batch-process`  
**Purpose:** Process multiple documents in parallel

```bash
curl -X POST "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/batch-process" \
  -H "Content-Type: multipart/form-data" \
  -F "files=@doc1.pdf" \
  -F "files=@doc2.png" \
  -F "files=@doc3.jpg" \
  -F 'config={"parallel_processing": true, "confidence_threshold": 0.8}'
```

**Response:**
```json
{
  "success": true,
  "batch_id": "batch_xyz789",
  "total_documents": 3,
  "processing_time_ms": 3200.5,
  "results": [
    {
      "success": true,
      "filename": "doc1.pdf",
      "extracted_text": "...",
      "confidence": 0.95,
      "processor_used": "INVOICE_PROCESSOR",
      "document_type": "invoice"
    },
    {
      "success": true,
      "filename": "doc2.png",
      "extracted_text": "...",
      "confidence": 0.88,
      "processor_used": "OCR_PROCESSOR",
      "document_type": "image"
    }
  ],
  "timestamp": "2024-01-15T10:30:00Z"
}
```

---

## ⚙️ Configuration Endpoints

### 1. API Configuration

**Endpoint:** `GET /api/v1/config`
**Purpose:** Get comprehensive API configuration and capabilities

```bash
curl -X GET "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/config"
```

**Response:**
```json
{
  "supported_formats": [
    "pdf", "png", "jpg", "jpeg", "gif", "bmp", "tiff", "webp", "svg",
    "docx", "doc", "xlsx", "xls", "pptx", "ppt", "txt", "csv", "html"
  ],
  "processors": {
    "google_document_ai": {
      "available": true,
      "processors": {
        "OCR_PROCESSOR": "General OCR processing",
        "FORM_PARSER_PROCESSOR": "Form and table extraction",
        "INVOICE_PROCESSOR": "Invoice processing",
        "EXPENSE_PROCESSOR": "Receipt and expense processing"
      },
      "capabilities": {
        "OCR_PROCESSOR": {
          "strengths": ["general_text", "handwriting", "multi_language"],
          "accuracy": 0.95,
          "speed": "fast",
          "max_pages": 15
        }
      }
    },
    "llm_router": {
      "available": true,
      "model": "gpt-4o",
      "enabled": true
    }
  },
  "features": {
    "llm_intelligent_routing": true,
    "google_document_ai": true,
    "universal_format_support": true,
    "parallel_processing": true,
    "insurance_optimization": true,
    "batch_processing": true,
    "multi_language_support": true,
    "table_extraction": true,
    "form_processing": true,
    "entity_extraction": true
  },
  "settings": {
    "max_file_size": ********,
    "max_workers": 8,
    "confidence_threshold": 0.85,
    "batch_size": 10,
    "cache_enabled": true,
    "debug_mode": false
  }
}
```

### 2. Supported File Formats

**Endpoint:** `GET /api/v1/supported-formats`
**Purpose:** Get detailed information about supported file formats

```bash
curl -X GET "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/supported-formats"
```

**Response:**
```json
{
  "total_formats": 32,
  "supported_formats": [
    "pdf", "png", "jpg", "jpeg", "gif", "bmp", "tiff", "webp", "svg",
    "docx", "doc", "xlsx", "xls", "pptx", "ppt", "txt", "csv", "html",
    "xml", "json", "yaml", "rtf", "odt", "ods", "odp", "zip", "7z"
  ],
  "categories": {
    "images": ["png", "jpg", "jpeg", "gif", "bmp", "tiff", "webp", "svg"],
    "documents": ["pdf"],
    "office": ["docx", "doc", "xlsx", "xls", "pptx", "ppt"],
    "text": ["txt", "csv", "json", "yaml", "rtf"],
    "web": ["html", "xml"],
    "archives": ["zip", "7z", "tar", "gz"],
    "open_office": ["odt", "ods", "odp"]
  },
  "processing_methods": {
    "direct_extraction": ["txt", "csv", "docx", "xlsx", "pptx", "html"],
    "ocr_required": ["png", "jpg", "jpeg", "gif", "bmp", "tiff", "webp"],
    "hybrid_processing": ["pdf"],
    "archive_extraction": ["zip", "7z"]
  },
  "google_ai_processors": {
    "OCR_PROCESSOR": "General text extraction",
    "FORM_PARSER_PROCESSOR": "Forms and tables",
    "INVOICE_PROCESSOR": "Invoices and bills",
    "EXPENSE_PROCESSOR": "Receipts and expenses"
  }
}
```

### 3. Processing Statistics

**Endpoint:** `GET /api/v1/stats`
**Purpose:** Get processing statistics and performance metrics

```bash
curl -X GET "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/stats"
```

**Response:**
```json
{
  "routing_statistics": {
    "total_requests": 1250,
    "successful_routes": 1225,
    "success_rate": 0.98,
    "average_routing_time": 1.2,
    "processor_usage": {
      "OCR_PROCESSOR": 450,
      "INVOICE_PROCESSOR": 320,
      "FORM_PARSER_PROCESSOR": 280,
      "EXPENSE_PROCESSOR": 175
    }
  },
  "processor_status": {
    "google_document_ai": {
      "status": "healthy",
      "available_processors": 12,
      "active_processors": 8,
      "total_processed": 1180
    }
  },
  "system_info": {
    "version": "2.0.0",
    "uptime": "72h 15m",
    "supported_formats": 32,
    "available_processors": 12
  },
  "performance_metrics": {
    "average_processing_time": "2.4s",
    "success_rate": "98.2%",
    "cache_hit_rate": "15.3%"
  }
}
```

---

## 🛠️ Utility Endpoints

### 1. Clear Cache

**Endpoint:** `POST /api/v1/clear-cache`
**Purpose:** Clear processing cache to free memory

```bash
curl -X POST "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/clear-cache"
```

**Response:**
```json
{
  "success": true,
  "message": "Cache cleared successfully",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

---

## 📊 Request/Response Models

### Processing Configuration Model

```json
{
  "llm_routing_enabled": true,
  "parallel_processing": true,
  "confidence_threshold": 0.85,
  "enable_fallbacks": true,
  "debug_mode": false,
  "insurance_optimization": true,
  "ocr_engine": "google",
  "google_processor": "OCR_PROCESSOR",
  "tesseract_psm": 6,
  "post_processing": "v1",
  "preprocessing": "auto",
  "preprocessing_force_level": null,
  "preprocessing_techniques": null
}
```

### Error Response Model

```json
{
  "success": false,
  "error": "File too large. Maximum size: ******** bytes",
  "status_code": 413,
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_error_123"
}
```

---

## ⚠️ Error Handling

### HTTP Status Codes

| Code | Description | Example |
|------|-------------|---------|
| `200` | Success | Successful processing |
| `400` | Bad Request | Invalid file format or configuration |
| `413` | Payload Too Large | File exceeds 50MB limit |
| `422` | Unprocessable Entity | Invalid JSON configuration |
| `500` | Internal Server Error | Processing failure |
| `503` | Service Unavailable | System overloaded or maintenance |

### Common Error Scenarios

1. **File Too Large**
   ```json
   {
     "success": false,
     "error": "File too large. Maximum size: ******** bytes",
     "status_code": 413
   }
   ```

2. **Unsupported Format**
   ```json
   {
     "success": false,
     "error": "Unsupported file format: .xyz",
     "status_code": 400
   }
   ```

3. **Processing Failure**
   ```json
   {
     "success": false,
     "error": "OCR processing failed: Unable to extract text",
     "status_code": 500
   }
   ```

---

## ⚡ Rate Limits & Performance

### Limits

| Parameter | Limit | Description |
|-----------|-------|-------------|
| **File Size** | 50MB | Maximum file size per request |
| **Batch Size** | 10 files | Maximum files per batch request |
| **Concurrent Requests** | 8 workers | Parallel processing capacity |
| **Request Timeout** | 5 minutes | Maximum processing time |

### Performance Optimization

- **Preprocessing**: Automatic image enhancement for better OCR accuracy
- **Intelligent Routing**: LLM selects optimal processor for each document type
- **Parallel Processing**: Multiple documents processed simultaneously
- **Caching**: Intelligent caching for repeated requests
- **Fallback Strategies**: Multiple OCR engines for reliability

---

## 📝 Examples

### Basic Text Extraction

```bash
# Simple image OCR
curl -X POST "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/extract-text" \
  -F "file=@receipt.jpg"

# PDF with custom configuration
curl -X POST "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/extract-text" \
  -F "file=@invoice.pdf" \
  -F 'config={"post_processing": "v2", "confidence_threshold": 0.9}'
```

### Advanced Processing

```bash
# Insurance document with optimization
curl -X POST "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/extract-text" \
  -F "file=@insurance_claim.pdf" \
  -F 'config={
    "insurance_optimization": true,
    "post_processing": "v1",
    "preprocessing": "enhanced",
    "debug_mode": true
  }'

# Batch processing multiple files
curl -X POST "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/batch-process" \
  -F "files=@doc1.pdf" \
  -F "files=@doc2.png" \
  -F "files=@doc3.jpg" \
  -F 'config={"parallel_processing": true}'
```

### Health Monitoring

```bash
# Quick readiness check
curl "https://zurich-ocr.dev-scc-demo.rozie.ai/readiness"

# Detailed health status
curl "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/health"

# System statistics
curl "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/stats"
```

---

## 🔗 Additional Resources

- **Interactive API Documentation**: [Swagger UI](https://zurich-ocr.dev-scc-demo.rozie.ai/docs)
- **Alternative Documentation**: [ReDoc](https://zurich-ocr.dev-scc-demo.rozie.ai/redoc)
- **Health Dashboard**: [Health Check](https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/health)
- **Configuration**: [API Config](https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/config)

---

**© 2024 Zurich OCR Engine - Advanced OCR Solution with LLM-Powered Intelligent Routing**
```
