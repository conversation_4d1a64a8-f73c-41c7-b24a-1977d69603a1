#!/bin/bash

# Multi-Format OCR API Test Script
# Tests all supported file formats

API_URL="http://localhost:8000/api/v1/extract-text"
CONFIG='{"vision_analysis": {"enabled": false}, "debug": {"enabled": false}}'

echo "=== Multi-Format OCR API Testing ==="
echo "Testing all supported file formats..."
echo

# Test 1: Text File (Direct extraction)
echo "1. Testing TXT file (Direct text extraction)..."
curl -s -X POST "$API_URL" \
  -F "file=@test_documents/sample.txt" \
  -F "config=$CONFIG" | jq '{
    success: .success,
    document_type: .metadata.document_type,
    engine_used: .metadata.engine_used,
    text_length: (.extracted_text | length),
    processing_time: .metadata.processing_time_ms
  }'
echo

# Test 2: CSV File (Structured data extraction)
echo "2. Testing CSV file (Structured data extraction)..."
curl -s -X POST "$API_URL" \
  -F "file=@test_documents/sample.csv" \
  -F "config=$CONFIG" | jq '{
    success: .success,
    document_type: .metadata.document_type,
    engine_used: .metadata.engine_used,
    text_length: (.extracted_text | length),
    structured_data_available: (.structured_data != null),
    processing_time: .metadata.processing_time_ms
  }'
echo

# Test 3: HTML File (Web content extraction)
echo "3. Testing HTML file (Web content extraction)..."
curl -s -X POST "$API_URL" \
  -F "file=@test_documents/sample.html" \
  -F "config=$CONFIG" | jq '{
    success: .success,
    document_type: .metadata.document_type,
    engine_used: .metadata.engine_used,
    text_length: (.extracted_text | length),
    text_preview: (.extracted_text | .[0:100]),
    processing_time: .metadata.processing_time_ms
  }'
echo

# Test 4: Image File (OCR processing)
echo "4. Testing PNG image (OCR processing)..."
if [ -f "test_documents/sample_invoice.png" ]; then
    curl -s -X POST "$API_URL" \
      -F "file=@test_documents/sample_invoice.png" \
      -F "config=$CONFIG" | jq '{
        success: .success,
        document_type: .metadata.document_type,
        engine_used: .metadata.engine_used,
        text_length: (.extracted_text | length),
        confidence: .metadata.confidence_score,
        processing_time: .metadata.processing_time_ms
      }'
else
    echo "No PNG test file found, skipping..."
fi
echo

# Test 5: PDF File (Text extraction + OCR fallback)
echo "5. Testing PDF file (Text extraction + OCR fallback)..."
if [ -f "test_documents/sample.pdf" ]; then
    curl -s -X POST "$API_URL" \
      -F "file=@test_documents/sample.pdf" \
      -F "config=$CONFIG" | jq '{
        success: .success,
        document_type: .metadata.document_type,
        engine_used: .metadata.engine_used,
        text_length: (.extracted_text | length),
        pages: .metadata.page_count,
        processing_time: .metadata.processing_time_ms
      }'
else
    echo "No PDF test file found, skipping..."
fi
echo

echo "=== Test Summary ==="
echo "Supported formats tested:"
echo "✓ TXT - Direct text extraction"
echo "✓ CSV - Structured data processing"
echo "✓ HTML - Web content parsing"
echo "✓ PNG - OCR processing (if available)"
echo "✓ PDF - Hybrid text/OCR processing (if available)"
echo
echo "Additional supported formats (not tested):"
echo "• DOCX - Word document processing"
echo "• XLSX - Excel spreadsheet processing"
echo "• PPTX - PowerPoint presentation processing"
echo "• MSG - Outlook email processing"
echo "• ZIP - Archive extraction and processing"
echo "• PBIX - PowerBI file metadata extraction"
echo
echo "For complete testing, add sample files for all formats to test_documents/"
