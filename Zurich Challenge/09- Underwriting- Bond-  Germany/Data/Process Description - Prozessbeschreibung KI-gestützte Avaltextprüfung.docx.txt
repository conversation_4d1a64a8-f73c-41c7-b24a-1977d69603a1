<PERSON>ns German45INTERNAL USE ONLYINTERNAL USE ONLYProcess Description: AI-Supported Guarantee Text Review1. Process ObjectiveThe purpose of this process is to automate the pre-screening of guarantee texts (bond/guarantee wording) submitted by clients using AI. The goal is to provide afeboencing with a structured, risk-based summary for decision-making — based on flexible, adjustable screening criteria.2. Process Overview 6-Step WorkflowStepDescription1. Client SubmissionThe client submits a customized guarantee text (PDF or Word format).2. AI AnalysisThe AI reviews the text based on predefined screening criteria and applies risk-based flags (traffic light logic).3. Flag & Comment GenerationThe AI generates specific comments for each flagged clause and creates a summary document with a full risk overview.4. Document Bundle CreationThe original document and the AI-generated risk report are compiled into a single file or digital view.5. afeboencing ReviewBased on the flags and comments: afeboencing either approves, requests revision, or rejects the wording.6. Client Feedback (optional)If needed, the client receives suggested changes or template alternatives.3. Review Components Analysis ModulesModuleFunctionText ComparisonSemantic and literal comparison with internal standard templatesClause DetectionIdentification of critical risk clauses (e.g., on-demand, waiver of defenses, jurisdiction)Traffic Light LogicClassification into: “Acceptable”, “Needs Review”, “Not Acceptable”Comment GeneratorExplanatory comments for afeboencing with reasoning and suggested editsFormat & Formality CheckValidation of file format, readability, completenessMarket & Jurisdiction LogicCountry-specific validation for DE/AT/CH or IPS/fronting risk handling4. Dynamic Criteria ManagementComponentDescriptionCentral Criteria TableContains all screening points, flag colors, notes, and business rulesResponsible PersonEach criterion is owned by a specific team member or roleChange LogAll updates are tracked (who, when, what, why)CategorizationGrouped into Legal, Formal, Commercial, RegulatoryMarket DependencyCriteria can be filtered by region, client type, or risk class5. Success Criteria & MilestonesMilestoneSuccess IndicatorFlagged Document Preview AvailableDocument viewer shows color-coded highlights and commentsCriteria Are Categorized & PrioritizedAll checks are assigned types and severity (e.g., critical, high, medium and low)Criteria Can Be Edited LiveRoles can add/change/delete screening rules; all changes are traceableAI Recommends ActionOutput includes suggestion: “Approve / Review / Reject”Reduced Review TimeTarget: reduce manual review effort by ≥ 40% per documentProzessbeschreibung: KI-gestützte Avaltextprüfung1. Ziel des ProzessesDie automatisierte Vorprüfung von Avaltexten (Garantie-/Bürgschaftsurkunden), die vom Kunden individuell eingereicht werden, mit Hilfe einer KI. Ziel ist es, dem afeboencing eine fundierte und strukturierte Entscheidungsgrundlage zu bieten – unter Berücksichtigung dynamisch anpassbarer Prüfkriterien und risikoorientierter Flags.2. ProzessüberblickAblauf in 6 SchrittenSchrittBeschreibung1. Eingang des AvaltextsKunde reicht individuellen Avaltext im Word- oder PDF-Format ein.2. KI-AnalyseKI prüft den Text anhand vordefinierter Prüfkriterien und kennzeichnet Risiken durch farbige Flags (Ampellogik).3. Ausgabe von Kommentaren und Flag-BerichtDie KI generiert risikobasierte Kommentare zu kritischen Textstellen und erzeugt ein Flag-Dokument (Übersicht + Detailansicht).4. Zusammenstellung der DokumenteOriginaltext + Flag-Dokument werden gebündelt zur Verfügung gestellt (PDF oder digitales Review-Tool).5. Entscheidung durch UnderwriterBasierend auf Flags und Kommentaren: Freigabe, Rückfrage oder Ablehnung.6. Rückmeldung an Kunde (optional)Falls notwendig, erfolgt Rückmeldung mit Änderungswünschen oder Standardtext-Vorschlägen.3. Struktur der PrüfungBestandteile der AnalyseModulFunktionTextvergleichAbgleich mit Standardformulierungen (semantisch & textlich)KlauselprüfungIdentifikation von Risikoklauseln (z. B. on-demand, Einredeverzicht, Rechtswahl)AmpellogikEinstufung in „Zeichnungsfähig“, „Prüfungsbedürftig“, „Nicht zeichnungsfähig“KommentarfunktionAutomatisch generierte Hinweise für afeboencing mit Erklärungen & VorschlägenFormalanalysePrüfung auf Dateiformat, Textstruktur, Lesbarkeit, VollständigkeitJuzoostertions- & MarktagrosBerücksichtigung von RP/IE/EU-spezifischen Sonderregelungen oder ZRP/ZUG-Vorgaben4. Verwaltung der Prüfkriterien (dynamisch)ElementBeschreibungZentrale Prüfkriterien-TabelleAlle Kriterien mit Ampelfarbe, Beschreibung, Kommentar, Pflicht/Erlaubnis/NogoVerantwortliche PersonJedes Kriterium ist einer Person/Gruppe zugewiesen, die es aktualisieren kannÄnderungshistorieJede Änderung wird protokolliert (Zeit, Nutzer, Änderung, Grund)Kategorisierungz. B. Formal, Rechtlich, Wirtschaftlich, RegulatorischMarktabhängigkeitKriterien entfernt oder ergänzt werden5. Erfolgskriterien & MeilensteineMeilensteinErfolgskriteriumDokumentenansicht mit Flags verfügbarDokument kann mit Hervorhebungen (rot/gelb/grün) und Kommentaren angesehen werdenKriterien sind kategorisiert & priorisiertPrüfkriterien sind nach Typ und Risiko (z. B. kritisch/hoch/mittel/niedrig) eingeteiltKriterien sind editierbar & dokumentiertÄnderungen sind möglich, rollenbasiert gesichert und nachvollziehbarKI liefert HandlungsempfehlungSystem schlägt „Freigabe / Rückfrage / Ablehnung“ vor – auf Basis der AmpelbewertungReviewzeit pro Dokument sinktZiel: Reduktion manueller Reviewzeit um ≥ 40 %INTERNAL USE ONLYINTERNAL USE ONLYINTERNAL USE ONLYINTERNAL USE ONLY