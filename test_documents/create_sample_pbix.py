"""
Create a sample PBIX-like file for testing
Since PBIX files are complex ZIP archives, we'll create a simplified version
"""

import zipfile
import json
import io

def create_sample_pbix():
    """Create a sample PBIX file structure for testing"""
    
    # Sample metadata
    metadata = {
        "version": "1.0",
        "name": "Sample Insurance Dashboard",
        "description": "Insurance claims and policy data analysis",
        "created": "2025-06-22",
        "tables": ["Claims", "Policies", "Customers", "Agents"]
    }
    
    # Sample data model
    data_model = {
        "name": "InsuranceModel",
        "tables": [
            {
                "name": "Claims",
                "columns": ["ClaimID", "PolicyID", "Amount", "Status", "Date"],
                "measures": ["TotalClaims", "AvgClaimAmount", "ClaimsCount"]
            },
            {
                "name": "Policies", 
                "columns": ["PolicyID", "CustomerID", "Premium", "Type", "StartDate"],
                "measures": ["TotalPremium", "PolicyCount", "AvgPremium"]
            },
            {
                "name": "Customers",
                "columns": ["CustomerID", "Name", "Age", "Location", "Segment"],
                "measures": ["CustomerCount", "AvgAge"]
            }
        ],
        "relationships": [
            {"from": "Claims.PolicyID", "to": "Policies.PolicyID"},
            {"from": "Policies.CustomerID", "to": "Customers.CustomerID"}
        ]
    }
    
    # Sample report layout
    report_layout = {
        "pages": [
            {
                "name": "Overview",
                "visuals": ["ClaimsChart", "PremiumTrend", "CustomerMap"]
            },
            {
                "name": "Claims Analysis", 
                "visuals": ["ClaimsByType", "ClaimsTimeline", "TopClaims"]
            }
        ]
    }
    
    # Create ZIP file (PBIX structure)
    pbix_buffer = io.BytesIO()
    
    with zipfile.ZipFile(pbix_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        # Add metadata
        zip_file.writestr('Metadata/metadata.json', json.dumps(metadata, indent=2))
        
        # Add data model
        zip_file.writestr('DataModel/model.json', json.dumps(data_model, indent=2))
        
        # Add report layout
        zip_file.writestr('Report/layout.json', json.dumps(report_layout, indent=2))
        
        # Add version info
        version_info = {"version": "3.0", "build": "14.0.1000.0"}
        zip_file.writestr('Version', json.dumps(version_info))
        
        # Add settings
        settings = {
            "dataRefresh": "automatic",
            "cacheMode": "import",
            "language": "en-US"
        }
        zip_file.writestr('Settings/settings.json', json.dumps(settings, indent=2))
        
        # Add sample data (CSV format)
        sample_claims = """ClaimID,PolicyID,Amount,Status,Date
C001,P001,5000,Approved,2025-01-15
C002,P002,12000,Pending,2025-02-20
C003,P001,3500,Rejected,2025-03-10
C004,P003,8000,Approved,2025-04-05"""
        
        zip_file.writestr('Data/claims.csv', sample_claims)
        
        # Add documentation
        documentation = """
# Insurance Dashboard PBIX File

This PowerBI file contains:
- Claims data analysis
- Policy performance metrics  
- Customer segmentation
- Agent performance tracking

## Data Sources:
- Claims database
- Policy management system
- Customer CRM
- Agent performance data

## Key Metrics:
- Total Claims: $2.5M
- Active Policies: 1,250
- Customer Satisfaction: 4.2/5
- Claims Processing Time: 3.2 days avg
        """
        
        zip_file.writestr('Documentation/readme.md', documentation)
    
    # Save to file
    pbix_buffer.seek(0)
    with open('sample_insurance_dashboard.pbix', 'wb') as f:
        f.write(pbix_buffer.getvalue())

    print("✅ Sample PBIX file created: sample_insurance_dashboard.pbix")
    print(f"📊 File size: {len(pbix_buffer.getvalue())} bytes")

if __name__ == "__main__":
    create_sample_pbix()
