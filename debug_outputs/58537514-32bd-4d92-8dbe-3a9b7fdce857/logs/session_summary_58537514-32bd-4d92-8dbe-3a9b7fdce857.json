{"request_id": "58537514-32bd-4d92-8dbe-3a9b7fdce857", "timestamp": "2025-06-22T13:00:57.661036", "configuration": {"processing_mode": "auto", "document_type": "auto", "preprocessing": {"enabled": true, "profile": "auto", "deskew": true, "denoise": true, "enhance_contrast": true, "binarize": false, "sharpen": false, "resize_factor": null, "custom_steps": null}, "ocr_strategy": {"engine": "auto", "fallback_enabled": true, "parallel_processing": false, "confidence_threshold": 0.8, "language_hints": null, "custom_config": null}, "vision_analysis": {"enabled": true, "cost_threshold": 0.005, "model": "gpt-4o-mini", "max_tokens": 300, "detail_level": "auto"}, "output": {"format": "json", "include_confidence": true, "include_metadata": true, "include_coordinates": false, "include_regions": false, "structured_extraction": false, "extract_tables": false, "extract_key_value_pairs": false}, "debug": {"enabled": false, "save_intermediate_images": false, "save_api_responses": false, "detailed_timing": false, "collect_all_outputs": false, "include_processing_steps": false, "save_vision_analysis": false, "performance_profiling": false}, "timeout_seconds": 300, "priority": "normal"}, "metadata": {"request_id": "58537514-32bd-4d92-8dbe-3a9b7fdce857", "processing_time_ms": 12.014389038085938, "engine_used": "direct_extraction", "engines_attempted": ["direct_extraction"], "vision_analysis_used": false, "preprocessing_applied": [], "confidence_score": 1.0, "document_type": "txt", "language_detected": null, "page_count": 1, "text_quality": null, "extraction_completeness": null, "performance_metrics": {"total_processing_time_ms": 12.014389038085938, "vision_analysis_time_ms": 0.0, "preprocessing_time_ms": 0.0, "ocr_processing_time_ms": 0.0, "post_processing_time_ms": 0.0, "peak_memory_usage_mb": 100.0, "cpu_usage_percent": 50.0, "pages_per_second": 83.2335291316082, "characters_per_second": 30380.23813303699, "overall_confidence": 1.0, "text_quality_score": 0.85}, "cost_breakdown": {"vision_api_cost": 0.0, "ocr_engine_cost": 0.0, "preprocessing_cost": 0.0, "total_cost": 0.0, "currency": "USD", "cost_per_page": 0.0}, "server_version": "1.0.0", "processing_node": null, "timestamp": "2025-06-22 13:00:57.658609"}, "debug_info": {"session_id": "58537514-32bd-4d92-8dbe-3a9b7fdce857", "debug_enabled": true, "processing_steps": [], "intermediate_files": [], "api_calls": [], "error_logs": [], "performance_profile": {"total_processing_time_ms": 12.014389038085938, "vision_analysis_time_ms": 0.0, "preprocessing_time_ms": 0.0, "ocr_processing_time_ms": 0.0, "post_processing_time_ms": 0.0, "peak_memory_usage_mb": 100.0, "cpu_usage_percent": 50.0, "pages_per_second": 83.2335291316082, "characters_per_second": 30380.23813303699, "overall_confidence": 1.0, "text_quality_score": 0.85}, "original_image_path": null, "processed_image_paths": [], "preprocessing_visualizations": [], "vision_request_data": null, "vision_response_data": null, "ocr_engine_configs": {}, "ocr_raw_responses": {}}, "session_summary": {"total_processing_time_ms": 12.014389038085938, "engines_used": ["direct_extraction"], "vision_analysis_used": false, "preprocessing_applied": [], "final_confidence": 1.0, "total_cost": 0.0}}