"""
Zurich OCR Engine - Main FastAPI Application
Advanced OCR solution with LLM-powered intelligent routing
"""

import asyncio
import io
import logging
import time
import uuid
import json
from typing import Dict, Any, Optional, List, Union
import traceback
from datetime import datetime
from enum import Enum

from fastapi import FastAPI, File, UploadFile, HTTPException, Form, BackgroundTasks, Depends
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

# Local imports - Award-winning components
from .core.config import settings
from .core.intelligent_router import intelligent_router, DocumentAnalysis, ProcessingStrategy
from .core.google_document_ai_hub import google_ai_hub, ProcessingResult
from .core.universal_format_engine import universal_format_engine, FileFormatInfo, ExtractionResult
from .core.gpt_postprocessor import gpt_postprocessor
from .core.gpt_postprocessor_v2 import gpt_postprocessor_v2
from .core.mixed_content_analyzer import mixed_content_analyzer
from .core.preprocessing_engine import preprocessing_engine
from .core.ocr_engine_router import ocr_engine_router

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# FastAPI application
app = FastAPI(
    title="Zurich OCR Engine",
    description="""
    # Advanced OCR Processing with LLM-Powered Intelligent Routing

    ## 🚀 Core Features
    - **GPT-4 Powered Routing**: Intelligent document analysis and processor selection
    - **Google Document AI**: Integration with 40+ specialized processors
    - **Universal Format Support**: 44+ file formats including PDF, images, Office documents
    - **Advanced OCR Challenges**: Handles handwriting, poor quality scans, complex layouts
    - **Insurance Optimization**: Specialized for insurance industry documents
    - **High-Performance**: Parallel processing with 50% speed optimization (V2)
    - **Comprehensive Quality**: Detailed confidence scoring and validation

    ## 📋 API Endpoints

    ### Core Processing
    - `POST /api/v1/extract-text` - Main text extraction with intelligent routing
    - `GET /api/v1/health` - System health and component status
    - `GET /api/v1/config` - Complete system configuration
    - `GET /api/v1/stats` - Processing statistics and metrics

    ### Configuration & Documentation
    - `GET /api/v1/configuration-examples` - Pre-configured examples for different use cases
    - `GET /api/v1/supported-formats` - Detailed format support information
    - `POST /api/v1/clear-cache` - Clear processing cache

    ## ⚙️ Configuration Options

    ### Processing Modes
    - **Speed Optimized (V2)**: 50% faster processing with maintained quality
    - **Quality Optimized (V1)**: Maximum accuracy with comprehensive analysis
    - **Direct OCR**: Bypass LLM routing for known document types
    - **Minimal Processing**: Fastest possible extraction

    ### OCR Engines
    - **Google Document AI**: 4 specialized processors (OCR, Form Parser, Invoice, Layout Parser)
    - **Tesseract**: Local processing with 14 page segmentation modes

    ### Post-Processing
    - **V1**: Full-featured with content regions and drawing detection
    - **V2**: Speed-optimized with 50% performance improvement
    - **Disabled**: Raw OCR output only

    ### Preprocessing
    - **Auto**: AI-controlled optimization based on document analysis
    - **Enhanced**: Aggressive image enhancement for poor quality documents
    - **Basic**: Standard preprocessing techniques
    - **Minimal**: Light preprocessing only
    - **Disabled**: No image preprocessing

    ## 📊 Supported File Formats (44 total)

    ### Images & PDFs
    `pdf`, `png`, `jpg`, `jpeg`, `gif`, `bmp`, `tiff`, `webp`, `svg`, `ico`

    ### Microsoft Office
    `docx`, `doc`, `xlsx`, `xls`, `pptx`, `ppt`, `pptm`

    ### Email & Web
    `msg`, `eml`, `html`, `htm`, `xml`, `mhtml`

    ### Text & Data
    `csv`, `txt`, `rtf`, `json`, `yaml`, `yml`

    ### Archives & Others
    `zip`, `rar`, `7z`, `tar`, `gz`, `bz2`, `pbix`, `pbit`, `eps`, `ps`, `ai`

    ## 🔧 Example Configurations

    ### Speed Optimized
    ```json
    {
        "llm_routing_enabled": true,
        "post_processing": "v2",
        "preprocessing": "auto",
        "debug_mode": false
    }
    ```

    ### Quality Optimized
    ```json
    {
        "llm_routing_enabled": true,
        "post_processing": "v1",
        "preprocessing": "enhanced",
        "debug_mode": true
    }
    ```

    ### Direct Google OCR
    ```json
    {
        "llm_routing_enabled": false,
        "ocr_engine": "google",
        "google_processor": "LAYOUT_PARSER_PROCESSOR",
        "post_processing": "v2"
    }
    ```

    ## 📈 Performance Metrics
    - **Processing Speed**: V2 configuration provides 50% speed improvement
    - **Accuracy**: 95%+ confidence for high-quality documents
    - **Format Coverage**: 44 supported file formats
    - **Concurrent Processing**: Up to 8 parallel workers
    - **File Size Limit**: 50MB per document

    ## 🔗 Quick Start
    1. Upload a document to `/api/v1/extract-text`
    2. Optionally provide configuration in the `config` form field
    3. Receive comprehensive results with extracted text, confidence scores, and metadata
    4. Use `/api/v1/configuration-examples` for pre-configured setups

    For detailed configuration options, visit `/api/v1/configuration-examples`
    """,
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    contact={
        "name": "Zurich OCR Engine",
        "url": "https://github.com/your-repo/zurich-ocr-engine",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
    tags_metadata=[
        {
            "name": "Core Processing",
            "description": "Main OCR processing endpoints with intelligent routing"
        },
        {
            "name": "System Information",
            "description": "Health checks, configuration, and system status"
        },
        {
            "name": "Configuration",
            "description": "Configuration examples and format information"
        },
        {
            "name": "Utilities",
            "description": "Cache management and utility functions"
        }
    ]
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration Enums for better documentation
class OCREngine(str, Enum):
    """Available OCR engines"""
    google = "google"
    tesseract = "tesseract"

class GoogleProcessor(str, Enum):
    """Google Document AI processor types"""
    OCR_PROCESSOR = "OCR_PROCESSOR"
    FORM_PARSER_PROCESSOR = "FORM_PARSER_PROCESSOR"
    INVOICE_PROCESSOR = "INVOICE_PROCESSOR"
    LAYOUT_PARSER_PROCESSOR = "LAYOUT_PARSER_PROCESSOR"

class PostProcessingVersion(str, Enum):
    """Post-processing versions"""
    v1 = "v1"
    v2 = "v2"
    disabled = "false"

class PreprocessingLevel(str, Enum):
    """Preprocessing levels"""
    auto = "auto"
    minimal = "minimal"
    basic = "basic"
    enhanced = "enhanced"
    aggressive = "aggressive"
    disabled = "false"

class PreprocessingTechnique(str, Enum):
    """Available preprocessing techniques"""
    grayscale = "grayscale"
    denoise = "denoise"
    deskew = "deskew"
    enhance_contrast = "enhance_contrast"
    sharpen = "sharpen"
    binarize = "binarize"
    morphological = "morphological"
    remove_lines = "remove_lines"
    remove_borders = "remove_borders"
    resize = "resize"

# Configuration Examples for Documentation
class ConfigurationExamples:
    """
    Common configuration examples for different use cases
    """

    SPEED_OPTIMIZED = {
        "llm_routing_enabled": True,
        "post_processing": "v2",
        "preprocessing": "auto",
        "debug_mode": False
    }

    QUALITY_OPTIMIZED = {
        "llm_routing_enabled": True,
        "post_processing": "v1",
        "preprocessing": "enhanced",
        "debug_mode": True
    }

    DIRECT_GOOGLE_OCR = {
        "llm_routing_enabled": False,
        "ocr_engine": "google",
        "google_processor": "LAYOUT_PARSER_PROCESSOR",
        "post_processing": "v2",
        "preprocessing": "basic"
    }

    DIRECT_TESSERACT = {
        "llm_routing_enabled": False,
        "ocr_engine": "tesseract",
        "tesseract_psm": 6,
        "post_processing": "v1",
        "preprocessing": "enhanced"
    }

    MINIMAL_PROCESSING = {
        "llm_routing_enabled": False,
        "ocr_engine": "google",
        "post_processing": False,
        "preprocessing": False,
        "debug_mode": False
    }

# Pydantic models for API requests and responses

class ExtractTextRequest(BaseModel):
    """
    Complete request model for text extraction API endpoint.

    This model documents the multipart form request structure for the
    /api/v1/extract-text endpoint, including file upload and configuration.

    Note: This is a documentation model. The actual endpoint uses FastAPI's
    UploadFile and Form parameters for multipart/form-data handling.
    """

    file: str = Field(
        description="""
        Document file to process (multipart form field: 'file')

        **Supported Formats (44 total):**

        **Images & PDFs:** pdf, png, jpg, jpeg, gif, bmp, tiff, webp, svg, ico

        **Microsoft Office:** docx, doc, xlsx, xls, pptx, ppt, pptm

        **Email & Web:** msg, eml, html, htm, xml, mhtml

        **Text & Data:** csv, txt, rtf, json, yaml, yml

        **Archives:** zip, rar, 7z, tar, gz, bz2

        **Specialized:** pbix, pbit, eps, ps, ai

        **File Size Limit:** 50MB per document

        **Processing Notes:**
        - PDF/Images: Full OCR processing with intelligent routing
        - Office Documents: Direct text extraction (faster, more accurate)
        - Archives: Automatic extraction and processing of contained files
        - Email: Full content and attachment processing
        """,
        example="insurance_claim.pdf"
    )

    config: Optional[str] = Field(
        default=None,
        description="""
        Processing configuration as JSON string (multipart form field: 'config')

        **If not provided:** Intelligent defaults based on document analysis

        **Configuration Structure:** See ProcessingConfig model for complete options

        **Common Examples:**

        **Speed Optimized (50% faster):**
        ```json
        {
            "llm_routing_enabled": true,
            "post_processing": "v2",
            "preprocessing": "auto",
            "debug_mode": false
        }
        ```

        **Quality Optimized (maximum accuracy):**
        ```json
        {
            "llm_routing_enabled": true,
            "post_processing": "v1",
            "preprocessing": "enhanced",
            "debug_mode": true
        }
        ```

        **Direct OCR (no LLM overhead):**
        ```json
        {
            "llm_routing_enabled": false,
            "ocr_engine": "google",
            "google_processor": "LAYOUT_PARSER_PROCESSOR",
            "post_processing": "v2"
        }
        ```

        **Insurance Specialized:**
        ```json
        {
            "llm_routing_enabled": true,
            "post_processing": "v1",
            "preprocessing": "enhanced",
            "insurance_optimization": true,
            "confidence_threshold": 0.9
        }
        ```
        """,
        example='{"llm_routing_enabled": true, "post_processing": "v2", "preprocessing": "auto"}'
    )

    class Config:
        schema_extra = {
            "example": {
                "file": "insurance_claim.pdf",
                "config": '{"llm_routing_enabled": true, "post_processing": "v2", "preprocessing": "auto", "debug_mode": false}'
            },
            "description": """
            **Request Format:** multipart/form-data

            **Required Fields:**
            - file: Document file (binary data)

            **Optional Fields:**
            - config: JSON configuration string

            **cURL Example:**
            ```bash
            curl -X POST "http://localhost:8000/api/v1/extract-text" \\
              -F "file=@document.pdf" \\
              -F 'config={"llm_routing_enabled": true, "post_processing": "v2"}'
            ```

            **JavaScript Example:**
            ```javascript
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('config', JSON.stringify({
                llm_routing_enabled: true,
                post_processing: "v2",
                preprocessing: "auto"
            }));

            fetch('/api/v1/extract-text', {
                method: 'POST',
                body: formData
            });
            ```
            """
        }

# Pydantic models for API requests and responses
class ProcessingConfig(BaseModel):
    """
    Comprehensive configuration for document processing with detailed options.

    This model defines all available configuration parameters for the OCR engine,
    including routing options, processing modes, and optimization settings.
    """

    # Core Routing Configuration
    llm_routing_enabled: bool = Field(
        default=True,
        description="Enable LLM-powered intelligent document analysis and processor routing. When enabled, GPT-4 analyzes the document to determine optimal processing strategy.",
        example=True
    )

    # Processing Options
    parallel_processing: bool = Field(
        default=True,
        description="Enable parallel processing for complex documents with multiple processors",
        example=True
    )
    confidence_threshold: float = Field(
        default=0.85,
        ge=0.0,
        le=1.0,
        description="Minimum confidence threshold for accepting OCR results (0.0-1.0)",
        example=0.85
    )
    enable_fallbacks: bool = Field(
        default=True,
        description="Enable fallback processing strategies when primary processor fails",
        example=True
    )
    debug_mode: bool = Field(
        default=False,
        description="Enable detailed debug information including step-by-step timing and intermediate results",
        example=False
    )
    insurance_optimization: bool = Field(
        default=True,
        description="Enable insurance industry-specific optimizations and document type recognition",
        example=True
    )

    # Direct OCR Engine Selection (bypasses LLM routing)
    ocr_engine: Optional[str] = Field(
        default=None,
        description="Direct OCR engine selection (bypasses LLM routing). Options: 'google', 'tesseract'",
        example="google"
    )

    # Google Document AI Configuration
    google_processor: Optional[str] = Field(
        default="OCR_PROCESSOR",
        description="Google Document AI processor type. Options: 'OCR_PROCESSOR', 'FORM_PARSER_PROCESSOR', 'INVOICE_PROCESSOR', 'LAYOUT_PARSER_PROCESSOR'",
        example="LAYOUT_PARSER_PROCESSOR"
    )

    # Tesseract Configuration
    tesseract_psm: Optional[int] = Field(
        default=6,
        ge=0,
        le=13,
        description="Tesseract Page Segmentation Mode (0-13). Common values: 3=fully automatic, 6=uniform block, 8=single word, 13=raw line",
        example=6
    )

    # Post-Processing Configuration
    post_processing: Union[str, bool] = Field(
        default="v1",
        description="Post-processing version. Options: 'v1' (full-featured), 'v2' (speed-optimized, 50% faster), false (disabled)",
        example="v2"
    )

    # Preprocessing Configuration
    preprocessing: Union[str, bool] = Field(
        default="auto",
        description="Image preprocessing level. Options: 'auto' (AI-controlled), 'minimal', 'basic', 'enhanced', 'aggressive', false (disabled)",
        example="auto"
    )
    preprocessing_force_level: Optional[str] = Field(
        default=None,
        description="Force specific preprocessing level, overriding AI analysis. Same options as 'preprocessing'",
        example="enhanced"
    )
    preprocessing_techniques: Optional[Dict[str, Union[bool, str]]] = Field(
        default=None,
        description="Fine-grained preprocessing control. Available techniques: 'grayscale', 'denoise', 'deskew', 'enhance_contrast', 'sharpen', 'binarize', 'morphological', 'remove_lines', 'remove_borders', 'resize'",
        example={"techniques": ["grayscale", "denoise", "deskew"]}
    )

class ProcessingResponse(BaseModel):
    """
    Comprehensive response from text extraction processing.

    Contains extracted text, confidence metrics, processing details,
    structured data extraction, and optional debug information for complete transparency.
    """

    # Core Results
    success: bool = Field(
        description="Whether processing completed successfully without errors",
        example=True
    )
    extracted_text: str = Field(
        description="Complete extracted text content from the document. Includes all readable text with preserved formatting where possible.",
        example="INSURANCE CLAIM FORM\\nPolicy Number: 12345\\nClaimant: John Doe\\nDate of Loss: 2024-01-15\\n..."
    )
    confidence: float = Field(
        description="Overall confidence score for the extraction (0.0-1.0). Higher values indicate better quality results.",
        ge=0.0,
        le=1.0,
        example=0.92
    )
    document_type: str = Field(
        description="Detected document type based on LLM analysis. Examples: 'insurance_claim', 'invoice', 'form', 'general_document'",
        example="insurance_claim"
    )
    processor_used: str = Field(
        description="Primary processor used for text extraction. Examples: 'OCR_PROCESSOR', 'FORM_PARSER_PROCESSOR', 'direct_extraction', 'tesseract_fallback'",
        example="LAYOUT_PARSER_PROCESSOR_+_gpt_postprocessing"
    )
    processing_time_ms: float = Field(
        description="Total processing time in milliseconds, including all steps from upload to final result",
        example=15420.5
    )

    # Structured Data Extraction
    entities: List[Dict[str, Any]] = Field(
        default=[],
        description="Extracted entities (names, dates, amounts, etc.) with confidence scores and positions",
        example=[
            {"type": "PERSON", "text": "John Doe", "confidence": 0.95, "start": 45, "end": 53},
            {"type": "DATE", "text": "2024-01-15", "confidence": 0.98, "start": 78, "end": 88}
        ]
    )
    tables: List[Dict[str, Any]] = Field(
        default=[],
        description="Extracted table data with headers, rows, and cell positions",
        example=[
            {
                "headers": ["Item", "Quantity", "Price"],
                "rows": [["Widget A", "2", "$10.00"], ["Widget B", "1", "$15.00"]],
                "confidence": 0.89
            }
        ]
    )
    form_fields: List[Dict[str, Any]] = Field(
        default=[],
        description="Extracted form fields with field names, values, and confidence scores",
        example=[
            {"field_name": "Policy Number", "field_value": "12345", "confidence": 0.96},
            {"field_name": "Claimant Name", "field_value": "John Doe", "confidence": 0.94}
        ]
    )

    # Processing Metadata
    metadata: Dict[str, Any] = Field(
        description="Comprehensive processing metadata including request details, file information, and processing configuration",
        example={
            "request_id": "123e4567-e89b-12d3-a456-************",
            "filename": "insurance_claim.pdf",
            "file_size": 245760,
            "text_length": 1250,
            "timestamp": "2024-01-15T10:30:45.123456",
            "processing_config": {
                "llm_routing_enabled": True,
                "post_processing": "v2",
                "preprocessing": "auto"
            },
            "preprocessing_info": {
                "applied": True,
                "techniques": ["grayscale", "denoise", "deskew"],
                "quality_improvement": 0.15,
                "processing_time": 245.2
            }
        }
    )
    format_info: Dict[str, Any] = Field(
        description="Detailed information about the detected file format and processing requirements",
        example={
            "extension": "pdf",
            "format_type": "pdf",
            "mime_type": "application/pdf",
            "requires_ocr": True,
            "page_count": 3,
            "is_searchable": False
        }
    )

    # Advanced Analysis Results
    document_analysis: Optional[Dict[str, Any]] = Field(
        default=None,
        description="LLM-powered document analysis results including document type, complexity, and processing recommendations",
        example={
            "document_type": "insurance_claim",
            "complexity": "medium",
            "language": "en",
            "has_handwriting": False,
            "has_tables": True,
            "has_forms": True,
            "recommended_processor": "FORM_PARSER_PROCESSOR",
            "confidence": 0.95
        }
    )
    processing_strategy: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Intelligent processing strategy determined by LLM routing, including primary and fallback processors",
        example={
            "primary_processor": "FORM_PARSER_PROCESSOR",
            "fallback_processors": ["OCR_PROCESSOR", "LAYOUT_PARSER_PROCESSOR"],
            "reasoning": "Document contains structured forms with clear field boundaries",
            "confidence_threshold": 0.8
        }
    )

    # GPT Post-Processing Results
    postprocessed_data: Optional[Dict[str, Any]] = Field(
        default=None,
        description="GPT-enhanced structured data with cleaned text, extracted fields, and enhanced formatting",
        example={
            "original_text": "Cleaned and formatted text...",
            "extracted_fields": {
                "policy_number": "12345",
                "claimant_name": "John Doe",
                "date_of_loss": "2024-01-15"
            },
            "document_summary": "Insurance claim form for vehicle damage",
            "key_information": ["Policy: 12345", "Claimant: John Doe", "Loss Date: 2024-01-15"]
        }
    )
    content_regions: Optional[List[Dict[str, Any]]] = Field(
        default=None,
        description="Identified content regions within the document (text blocks, forms, tables, etc.)",
        example=[
            {
                "type": "header",
                "content": "INSURANCE CLAIM FORM",
                "confidence": 0.98,
                "position": {"x": 100, "y": 50, "width": 400, "height": 30}
            },
            {
                "type": "form_section",
                "content": "Policy Information",
                "confidence": 0.95,
                "position": {"x": 50, "y": 100, "width": 500, "height": 200}
            }
        ]
    )
    drawings_metadata: Optional[List[Dict[str, Any]]] = Field(
        default=None,
        description="Metadata about drawings, diagrams, or non-text elements found in the document",
        example=[
            {
                "type": "diagram",
                "description": "Vehicle damage diagram",
                "position": {"x": 300, "y": 400, "width": 200, "height": 150},
                "confidence": 0.87
            }
        ]
    )

    # Performance and Debug Information
    step_timings: Optional[Dict[str, float]] = Field(
        default=None,
        description="Detailed timing information for each processing step in milliseconds",
        example={
            "1_format_detection_and_extraction": 125.3,
            "1.5_preprocessing": 245.2,
            "2_llm_routing_analysis": 1850.7,
            "2.5_preprocessing_update": 180.1,
            "3_ocr_processing": 8420.5,
            "4_gpt_postprocessing": 4905.2
        }
    )
    gpt_substep_timings: Optional[Dict[str, float]] = Field(
        default=None,
        description="Detailed timing information for GPT post-processing substeps in milliseconds",
        example={
            "content_analysis": 1250.3,
            "text_cleaning": 890.7,
            "field_extraction": 1420.2,
            "structure_enhancement": 1344.0
        }
    )

    # Error Handling
    debug_info: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Comprehensive debug information including processing decisions, intermediate results, and performance metrics. Only included when debug_mode=true",
        example={
            "processing_decisions": {
                "routing_reason": "Complex layout with forms detected",
                "fallback_triggered": False,
                "preprocessing_level_chosen": "enhanced"
            },
            "intermediate_results": {
                "preprocessing_improvement": "15%",
                "llm_confidence": 0.95,
                "ocr_raw_confidence": 0.88
            },
            "performance_metrics": {
                "total_api_calls": 3,
                "total_tokens_used": 2450,
                "cache_hits": 1
            }
        }
    )
    error_message: Optional[str] = Field(
        default=None,
        description="Detailed error message if processing failed, including troubleshooting guidance",
        example=None
    )

class HealthResponse(BaseModel):
    """Health check response"""
    status: str
    timestamp: str
    version: str
    components: Dict[str, Any]

class ConfigResponse(BaseModel):
    """Configuration response"""
    supported_formats: List[str]
    processors: Dict[str, Any]
    features: Dict[str, Any]
    settings: Dict[str, Any]

# API Endpoints

@app.get("/api/v1/health", response_model=HealthResponse, tags=["System Information"])
async def health_check():
    """
    Comprehensive health check endpoint

    Returns detailed status of all system components including:
    - LLM routing system
    - Google Document AI integration
    - Universal format engine
    - Processing statistics
    """

    try:
        # Check component health
        components = {
            "llm_router": {
                "status": "healthy" if intelligent_router.llm_client else "disabled",
                "statistics": intelligent_router.get_processing_statistics()
            },
            "google_ai_hub": {
                "status": "healthy" if google_ai_hub.client else "disabled",
                "processor_status": google_ai_hub.get_processor_status()
            },
            "universal_format_engine": {
                "status": "healthy",
                "supported_formats": len(universal_format_engine.supported_formats)
            },
            "settings": {
                "llm_routing_enabled": settings.LLM_ROUTING_ENABLED,
                "parallel_processing": settings.PARALLEL_PROCESSING,
                "debug_mode": settings.DEBUG_MODE
            }
        }

        return HealthResponse(
            status="healthy",
            timestamp=datetime.now().isoformat(),
            version="2.0.0",
            components=components
        )

    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return HealthResponse(
            status="unhealthy",
            timestamp=datetime.now().isoformat(),
            version="2.0.0",
            components={"error": str(e)}
        )

@app.post("/api/v1/extract-text", response_model=ProcessingResponse, tags=["Core Processing"])
async def extract_text(
    file: UploadFile = File(
        ...,
        description="Document file to process. Supports 44+ formats including PDF, images, Office documents, archives, and more. Maximum size: 50MB.",
        example="insurance_claim.pdf"
    ),
    config: Optional[str] = Form(
        None,
        description="Optional JSON configuration string. See ExtractTextRequest model and /api/v1/configuration-examples for detailed options.",
        example='{"llm_routing_enabled": true, "post_processing": "v2", "preprocessing": "auto"}'
    )
):
    """
    ## Advanced Text Extraction with LLM-Powered Intelligent Routing

    **Primary endpoint for document text extraction with comprehensive OCR processing.**

    ### 🚀 Core Features
    - **GPT-4 Intelligent Routing**: Analyzes documents to select optimal processing strategy
    - **Google Document AI**: 4 specialized processors (OCR, Form Parser, Invoice, Layout Parser)
    - **Universal Format Support**: 44+ file formats with intelligent processing
    - **Advanced OCR Challenges**: Handles poor quality scans, handwriting, complex layouts
    - **Insurance Optimization**: Specialized processing for insurance industry documents
    - **High-Performance**: Parallel processing with 50% speed optimization (V2)

    ### 📋 Request Format
    **Content-Type:** `multipart/form-data`

    **Required Parameters:**
    - `file`: Document file (binary data)

    **Optional Parameters:**
    - `config`: JSON configuration string (see examples below)

    ### ⚙️ Configuration Examples

    **Speed Optimized (50% faster):**
    ```json
    {
        "llm_routing_enabled": true,
        "post_processing": "v2",
        "preprocessing": "auto",
        "debug_mode": false
    }
    ```

    **Quality Optimized (maximum accuracy):**
    ```json
    {
        "llm_routing_enabled": true,
        "post_processing": "v1",
        "preprocessing": "enhanced",
        "debug_mode": true
    }
    ```

    **Direct Google OCR (no LLM overhead):**
    ```json
    {
        "llm_routing_enabled": false,
        "ocr_engine": "google",
        "google_processor": "LAYOUT_PARSER_PROCESSOR",
        "post_processing": "v2"
    }
    ```

    **Insurance Claims Specialized:**
    ```json
    {
        "llm_routing_enabled": true,
        "post_processing": "v1",
        "preprocessing": "enhanced",
        "insurance_optimization": true,
        "confidence_threshold": 0.9
    }
    ```

    ### 📊 Supported File Formats (44 total)

    | **Category** | **Formats** | **Processing Method** |
    |--------------|-------------|----------------------|
    | **Images & PDFs** | pdf, png, jpg, jpeg, gif, bmp, tiff, webp, svg, ico | Full OCR with intelligent routing |
    | **Microsoft Office** | docx, doc, xlsx, xls, pptx, ppt, pptm | Direct text extraction (faster) |
    | **Email & Web** | msg, eml, html, htm, xml, mhtml | Content and attachment processing |
    | **Text & Data** | csv, txt, rtf, json, yaml, yml | Direct extraction with validation |
    | **Archives** | zip, rar, 7z, tar, gz, bz2 | Automatic extraction and processing |
    | **Specialized** | pbix, pbit, eps, ps, ai | Format-specific processing |

    ### 🔧 Processing Pipeline

    1. **Format Detection**: Automatic file type identification
    2. **Preprocessing** (optional): Image enhancement for better OCR
    3. **LLM Analysis** (optional): Document type and complexity analysis
    4. **OCR Processing**: Google Document AI or Tesseract with intelligent routing
    5. **Post-Processing** (optional): GPT-powered text cleaning and structuring
    6. **Response Generation**: Comprehensive results with metadata

    ### 📈 Performance Metrics
    - **Processing Speed**: V2 configuration provides 50% speed improvement
    - **Accuracy**: 95%+ confidence for high-quality documents
    - **Concurrent Processing**: Up to 8 parallel workers
    - **File Size Limit**: 50MB per document

    ### 🔗 Related Endpoints
    - `GET /api/v1/configuration-examples` - Pre-configured examples
    - `GET /api/v1/supported-formats` - Detailed format information
    - `GET /api/v1/health` - System status and capabilities

    ### 💡 Usage Tips
    - Use `debug_mode: true` for detailed processing information
    - Enable `insurance_optimization` for insurance industry documents
    - Use `post_processing: "v2"` for 50% faster processing
    - Set `preprocessing: "enhanced"` for poor quality documents
    """

    start_time = time.time()
    request_id = str(uuid.uuid4())
    filename = file.filename or f"document_{request_id[:8]}"

    # Initialize step timing tracker
    step_timings = {}
    step_start_time = start_time

    logger.info(f"Starting processing for {filename} (ID: {request_id})")

    try:
        # Parse configuration
        processing_config = ProcessingConfig()
        if config:
            try:
                config_dict = json.loads(config)
                processing_config = ProcessingConfig(**config_dict)
            except Exception as e:
                logger.warning(f"Invalid config provided, using defaults: {str(e)}")

        # Read file data
        file_data = await file.read()

        # Validate file size
        if len(file_data) > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=413,
                detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE} bytes"
            )

        # Step 1: Universal format detection and extraction
        logger.info(f"Detecting format for {filename}")
        step_start_time = time.time()
        format_info = await universal_format_engine.detect_format(file_data, filename)
        extraction_result = await universal_format_engine.extract_content(file_data, filename)
        step_timings["1_format_detection_and_extraction"] = (time.time() - step_start_time) * 1000

        # Step 1.5: Preprocessing (if enabled and applicable)
        preprocessed_file_data = file_data
        preprocessing_result = None

        if (processing_config.preprocessing and
            processing_config.preprocessing != False and
            format_info.format_type in ["image", "pdf"]):

            logger.info(f"Starting preprocessing for {filename}")
            step_start_time = time.time()

            # Determine preprocessing level
            preprocessing_level = processing_config.preprocessing
            if processing_config.preprocessing_force_level:
                preprocessing_level = processing_config.preprocessing_force_level

            preprocessing_result = await preprocessing_engine.preprocess_image(
                file_data,
                level=preprocessing_level,
                format_info=format_info,
                document_analysis=None,  # Will be updated after LLM analysis
                force_techniques=processing_config.preprocessing_techniques.get("techniques") if processing_config.preprocessing_techniques else None
            )

            if preprocessing_result.success:
                preprocessed_file_data = preprocessing_result.processed_image_data
                logger.info(f"Preprocessing completed: {len(preprocessing_result.techniques_applied)} techniques, {preprocessing_result.quality_improvement_estimate:.1%} improvement")
            else:
                logger.warning(f"Preprocessing failed: {preprocessing_result.error_message}")

            step_timings["1.5_preprocessing"] = (time.time() - step_start_time) * 1000
        else:
            step_timings["1.5_preprocessing"] = 0.0  # Skipped

        # Step 2: LLM-powered intelligent routing (if OCR is required)
        document_analysis = None
        processing_strategy = None

        # Smart LLM routing with format-based overrides (anti-hallucination)
        office_formats = {"xlsx", "docx", "pptx", "txt", "csv", "html", "rtf"}
        should_skip_llm = format_info.extension.lower() in office_formats

        should_use_llm_routing = (
            processing_config.llm_routing_enabled and
            not should_skip_llm and  # Never route office files through LLM
            (extraction_result.requires_ocr or format_info.format_type == "image")
        )

        if should_skip_llm and processing_config.llm_routing_enabled:
            logger.info(f"Skipping LLM routing for office format: {format_info.extension}")

        if should_use_llm_routing:
            logger.info(f"Performing LLM analysis for {filename}")
            step_start_time = time.time()
            document_analysis = await intelligent_router.analyze_document(
                preprocessed_file_data, filename, {"format_info": format_info.__dict__}
            )
            processing_strategy = await intelligent_router.create_processing_strategy(document_analysis)
            step_timings["2_llm_routing_analysis"] = (time.time() - step_start_time) * 1000

            # Update preprocessing with document analysis if needed
            if (preprocessing_result and preprocessing_result.success and
                processing_config.preprocessing == "auto"):
                logger.info("Updating preprocessing based on document analysis")
                step_start_time = time.time()

                updated_preprocessing = await preprocessing_engine.preprocess_image(
                    file_data,
                    level="auto",
                    format_info=format_info,
                    document_analysis=document_analysis
                )

                if updated_preprocessing.success:
                    preprocessed_file_data = updated_preprocessing.processed_image_data
                    preprocessing_result = updated_preprocessing

                step_timings["2.5_preprocessing_update"] = (time.time() - step_start_time) * 1000
            else:
                step_timings["2.5_preprocessing_update"] = 0.0

        elif not processing_config.llm_routing_enabled and processing_config.ocr_engine:
            # Direct OCR engine routing (no LLM)
            logger.info(f"Direct OCR routing: {processing_config.ocr_engine}")
            step_timings["2_llm_routing_analysis"] = 0.0  # Skipped
            step_timings["2.5_preprocessing_update"] = 0.0  # Skipped
            document_analysis = None
            processing_strategy = None
        else:
            step_timings["2_llm_routing_analysis"] = 0.0  # Skipped
            step_timings["2.5_preprocessing_update"] = 0.0  # Skipped

        # Step 3: OCR Processing (Google Document AI or Direct Engine)
        google_ai_result = None

        if processing_strategy and (extraction_result.requires_ocr or format_info.format_type == "image"):
            # LLM-routed processing
            logger.info(f"Processing with Google Document AI using {processing_strategy.primary_processor}")
            step_start_time = time.time()
            google_ai_result = await google_ai_hub.process_document(
                preprocessed_file_data, processing_strategy, filename
            )
            step_timings["3_ocr_processing"] = (time.time() - step_start_time) * 1000

        elif not processing_config.llm_routing_enabled and processing_config.ocr_engine and (extraction_result.requires_ocr or format_info.format_type == "image"):
            # Direct engine routing
            logger.info(f"Direct OCR processing with {processing_config.ocr_engine}")
            step_start_time = time.time()

            ocr_result = await ocr_engine_router.process_with_engine(
                preprocessed_file_data,
                filename,
                processing_config.ocr_engine,
                processor=processing_config.google_processor,
                psm_mode=processing_config.tesseract_psm
            )

            # Convert OCR result to Google AI result format for compatibility
            if ocr_result.success:
                from zurich_ocr.core.google_document_ai_hub import ProcessingResult
                google_ai_result = ProcessingResult(
                    success=True,
                    text=ocr_result.text,
                    confidence=ocr_result.confidence,
                    processor_used=ocr_result.processor_used,
                    processing_time=ocr_result.processing_time,
                    page_count=1,
                    entities=[],
                    tables=[],
                    form_fields=[]
                )
            else:
                from zurich_ocr.core.google_document_ai_hub import ProcessingResult
                google_ai_result = ProcessingResult(
                    success=False,
                    text="",
                    confidence=0.0,
                    processor_used=ocr_result.processor_used,
                    processing_time=ocr_result.processing_time,
                    page_count=0,
                    entities=[],
                    tables=[],
                    form_fields=[],
                    error_message=ocr_result.error_message
                )

            step_timings["3_ocr_processing"] = (time.time() - step_start_time) * 1000
        else:
            step_timings["3_ocr_processing"] = 0.0  # Skipped

            # If Google Document AI failed, try Tesseract OCR fallback for images
            if not google_ai_result.success and format_info.format_type == "image":
                logger.info(f"Google Document AI failed, trying Tesseract OCR fallback for {filename}")
                step_start_time = time.time()
                try:
                    import pytesseract
                    from PIL import Image
                    import io

                    image = Image.open(io.BytesIO(file_data))
                    tesseract_text = pytesseract.image_to_string(image, config='--psm 6')

                    if tesseract_text.strip():
                        # Create a successful result from Tesseract
                        from zurich_ocr.core.google_document_ai_hub import ProcessingResult
                        google_ai_result = ProcessingResult(
                            success=True,
                            text=tesseract_text.strip(),
                            confidence=0.8,  # Reasonable confidence for Tesseract
                            processor_used="tesseract_fallback",
                            processing_time=0.5,
                            page_count=1,
                            entities=[],
                            tables=[],
                            form_fields=[]
                        )
                        logger.info(f"Tesseract OCR fallback successful for {filename}")
                        step_timings["3a_tesseract_fallback"] = (time.time() - step_start_time) * 1000
                except Exception as e:
                    logger.warning(f"Tesseract OCR fallback also failed for {filename}: {str(e)}")
                    step_timings["3a_tesseract_fallback"] = (time.time() - step_start_time) * 1000
            else:
                step_timings["3a_tesseract_fallback"] = 0.0  # Not needed

        # Step 4: GPT Post-Processing (if enabled)
        postprocessed_result = None

        if (processing_config.post_processing and
            processing_config.post_processing != False and
            google_ai_result and google_ai_result.success):

            logger.info(f"Starting GPT post-processing V{processing_config.post_processing} for {filename}")
            step_start_time = time.time()

            # Prepare Google Document AI output for post-processing
            raw_ocr_output = {
                'text': google_ai_result.text,
                'entities': google_ai_result.entities,
                'tables': google_ai_result.tables,
                'form_fields': google_ai_result.form_fields,
                'confidence': google_ai_result.confidence,
                'processor_used': google_ai_result.processor_used,
                'page_count': google_ai_result.page_count
            }

            # Determine document type for specialized processing
            doc_type = "insurance"  # Default
            if document_analysis:
                doc_type = document_analysis.document_type

            # Choose post-processing version
            if processing_config.post_processing == "v2":
                # V2 Speed-optimized post-processing
                postprocessed_result_v2 = await gpt_postprocessor_v2.process_document(
                    raw_ocr_output, doc_type
                )

                # Convert V2 result to V1 format for compatibility
                if postprocessed_result_v2.success:
                    from zurich_ocr.core.gpt_postprocessor import ProcessedContent, ContentRegion
                    postprocessed_result = ProcessedContent(
                        structured_data=postprocessed_result_v2.structured_data,
                        content_regions=[],  # V2 doesn't generate content regions
                        drawings_metadata=[],  # V2 doesn't generate drawings metadata
                        processing_time=postprocessed_result_v2.processing_time,
                        confidence=postprocessed_result_v2.confidence,
                        success=True,
                        substep_timings=postprocessed_result_v2.substep_timings
                    )
                else:
                    from zurich_ocr.core.gpt_postprocessor import ProcessedContent
                    postprocessed_result = ProcessedContent(
                        structured_data={},
                        content_regions=[],
                        drawings_metadata=[],
                        processing_time=postprocessed_result_v2.processing_time,
                        confidence=0.0,
                        success=False,
                        error_message=postprocessed_result_v2.error_message,
                        substep_timings=postprocessed_result_v2.substep_timings
                    )
            else:
                # V1 Full-featured post-processing
                postprocessed_result = await gpt_postprocessor.process_document(
                    raw_ocr_output, doc_type
                )

            step_timings["4_gpt_postprocessing"] = (time.time() - step_start_time) * 1000
            logger.info(f"GPT post-processing V{processing_config.post_processing} completed in {postprocessed_result.processing_time:.1f}s")
        else:
            step_timings["4_gpt_postprocessing"] = 0.0  # Skipped

        # Step 5: Combine results
        final_text = ""
        confidence = 0.0
        processor_used = "direct_extraction"
        entities = []
        tables = []
        form_fields = []

        # Post-processing results
        postprocessed_data = None
        content_regions = None
        drawings_metadata = None
        gpt_substep_timings = None

        if postprocessed_result and postprocessed_result.success:
            # Use GPT post-processed results (highest quality)
            postprocessed_data = postprocessed_result.structured_data
            content_regions = [region.__dict__ for region in postprocessed_result.content_regions] if postprocessed_result.content_regions else None
            drawings_metadata = postprocessed_result.drawings_metadata
            gpt_substep_timings = postprocessed_result.substep_timings

            # Use cleaned text if available, otherwise original
            if postprocessed_result.structured_data.get("original_text"):
                final_text = postprocessed_result.structured_data["original_text"]
            elif google_ai_result:
                final_text = google_ai_result.text

            confidence = min(google_ai_result.confidence + 0.1, 1.0) if google_ai_result else 0.9
            processor_used = f"{google_ai_result.processor_used}_+_gpt_postprocessing" if google_ai_result else "gpt_postprocessing"

        elif google_ai_result and google_ai_result.success:
            # Use Google Document AI results
            final_text = google_ai_result.text
            confidence = google_ai_result.confidence
            processor_used = google_ai_result.processor_used
            entities = google_ai_result.entities
            tables = google_ai_result.tables
            form_fields = google_ai_result.form_fields
        elif extraction_result.success and extraction_result.text:
            # Use direct extraction results
            final_text = extraction_result.text
            confidence = 0.95  # High confidence for direct extraction
            processor_used = "direct_extraction"
        else:
            # Fallback
            final_text = extraction_result.error_message or "No text could be extracted"
            confidence = 0.0
            processor_used = "fallback"

        # Calculate processing time
        processing_time_ms = (time.time() - start_time) * 1000

        # Create comprehensive response
        response = ProcessingResponse(
            success=bool(final_text and confidence > 0),
            extracted_text=final_text,
            confidence=confidence,
            document_type=document_analysis.document_type if document_analysis else format_info.format_type,
            processor_used=processor_used,
            processing_time_ms=processing_time_ms,
            entities=entities,
            tables=tables,
            form_fields=form_fields,
            metadata={
                "request_id": request_id,
                "filename": filename,
                "file_size": len(file_data),
                "text_length": len(final_text),
                "timestamp": datetime.now().isoformat(),
                "processing_config": processing_config.dict(),
                "preprocessing_info": {
                    "applied": preprocessing_result.success if preprocessing_result else False,
                    "techniques": preprocessing_result.techniques_applied if preprocessing_result else [],
                    "quality_improvement": preprocessing_result.quality_improvement_estimate if preprocessing_result else 0.0,
                    "processing_time": preprocessing_result.processing_time if preprocessing_result else 0.0
                } if preprocessing_result else None
            },
            format_info=format_info.__dict__,
            document_analysis=document_analysis.__dict__ if document_analysis else None,
            processing_strategy=processing_strategy.__dict__ if processing_strategy else None,
            postprocessed_data=postprocessed_data,
            content_regions=content_regions,
            drawings_metadata=drawings_metadata,
            step_timings=step_timings,
            gpt_substep_timings=gpt_substep_timings,
            debug_info={
                "extraction_result": extraction_result.__dict__,
                "google_ai_result": {
                    "success": google_ai_result.success,
                    "text": google_ai_result.text,
                    "confidence": google_ai_result.confidence,
                    "processor_used": google_ai_result.processor_used,
                    "processing_time": google_ai_result.processing_time,
                    "page_count": google_ai_result.page_count,
                    "entities": google_ai_result.entities,
                    "tables": google_ai_result.tables,
                    "form_fields": google_ai_result.form_fields,
                    "error_message": google_ai_result.error_message,
                    "raw_response": None  # Exclude raw response to avoid serialization issues
                } if google_ai_result else None,
                "gpt_postprocessing_result": {
                    "success": postprocessed_result.success,
                    "processing_time": postprocessed_result.processing_time,
                    "confidence": postprocessed_result.confidence,
                    "content_regions_count": len(postprocessed_result.content_regions),
                    "drawings_count": len(postprocessed_result.drawings_metadata),
                    "structured_data_keys": list(postprocessed_result.structured_data.keys()) if postprocessed_result.structured_data else [],
                    "error_message": postprocessed_result.error_message
                } if postprocessed_result else None
            } if processing_config.debug_mode else None
        )

        logger.info(f"Processing completed for {filename}: {confidence:.3f} confidence, {processing_time_ms:.1f}ms")

        return response

    except HTTPException:
        raise
    except Exception as e:
        processing_time_ms = (time.time() - start_time) * 1000
        error_message = f"Processing error: {str(e)}"

        logger.error(f"Processing failed for {filename}: {error_message}")
        logger.error(traceback.format_exc())

        return ProcessingResponse(
            success=False,
            extracted_text="",
            confidence=0.0,
            document_type="unknown",
            processor_used="error",
            processing_time_ms=processing_time_ms,
            metadata={
                "request_id": request_id,
                "filename": filename,
                "timestamp": datetime.now().isoformat(),
                "error": error_message
            },
            format_info={},
            error_message=error_message
        )

@app.get("/api/v1/config", response_model=ConfigResponse, tags=["System Information"])
async def get_config():
    """
    Get comprehensive API configuration

    Returns detailed information about:
    - Supported file formats (30+ formats)
    - Available processors and capabilities
    - System features and settings
    - Processing options and limits
    """

    return ConfigResponse(
        supported_formats=settings.SUPPORTED_FORMATS,
        processors={
            "google_document_ai": {
                "available": google_ai_hub.client is not None,
                "processors": settings.GOOGLE_PROCESSORS,
                "capabilities": settings.PROCESSOR_CAPABILITIES
            },
            "llm_router": {
                "available": intelligent_router.llm_client is not None,
                "model": settings.LLM_MODEL,
                "enabled": settings.LLM_ROUTING_ENABLED
            }
        },
        features={
            "llm_intelligent_routing": settings.LLM_ROUTING_ENABLED,
            "google_document_ai": google_ai_hub.client is not None,
            "universal_format_support": True,
            "parallel_processing": settings.PARALLEL_PROCESSING,
            "insurance_optimization": True,
            "advanced_ocr_challenges": True,
            "quality_assessment": True,
            "batch_processing": True,
            "comprehensive_debugging": True,
            "multi_language_support": True,
            "handwriting_recognition": True,
            "table_extraction": True,
            "form_processing": True,
            "entity_extraction": True
        },
        settings={
            "max_file_size": settings.MAX_FILE_SIZE,
            "max_workers": settings.MAX_WORKERS,
            "confidence_threshold": settings.DEFAULT_CONFIDENCE_THRESHOLD,
            "batch_size": settings.BATCH_SIZE,
            "cache_enabled": settings.ENABLE_CACHE,
            "debug_mode": settings.DEBUG_MODE
        }
    )

@app.get("/api/v1/stats", tags=["System Information"])
async def get_processing_stats():
    """
    Get processing statistics and performance metrics

    Returns:
    - Document processing statistics
    - Processor usage metrics
    - Performance data
    - Cache statistics
    """

    try:
        router_stats = intelligent_router.get_processing_statistics()
        processor_status = google_ai_hub.get_processor_status()

        return {
            "routing_statistics": router_stats,
            "processor_status": processor_status,
            "system_info": {
                "version": "2.0.0",
                "uptime": "N/A",  # Could be implemented with startup time tracking
                "supported_formats": len(settings.SUPPORTED_FORMATS),
                "available_processors": len(settings.GOOGLE_PROCESSORS)
            },
            "performance_metrics": {
                "average_processing_time": "N/A",  # Could be implemented with metrics collection
                "success_rate": "N/A",
                "cache_hit_rate": "N/A"
            }
        }

    except Exception as e:
        logger.error(f"Error getting stats: {str(e)}")
        return {"error": str(e)}

@app.post("/api/v1/batch-process")
async def batch_process_documents(
    files: List[UploadFile] = File(...),
    config: Optional[str] = Form(None)
):
    """
    Batch process multiple documents

    Process multiple documents in parallel with intelligent routing.
    Optimized for high-throughput document processing.

    Args:
        files: List of document files to process
        config: Optional JSON configuration for processing parameters

    Returns:
        List of processing results for each document
    """

    start_time = time.time()
    batch_id = str(uuid.uuid4())

    logger.info(f"Starting batch processing for {len(files)} documents (Batch ID: {batch_id})")

    try:
        # Parse configuration
        processing_config = ProcessingConfig()
        if config:
            try:
                config_dict = json.loads(config)
                processing_config = ProcessingConfig(**config_dict)
            except Exception as e:
                logger.warning(f"Invalid config provided, using defaults: {str(e)}")

        # Process documents in parallel
        results = []

        # Create tasks for parallel processing
        tasks = []
        for file in files:
            # Read file data
            file_data = await file.read()
            filename = file.filename or f"document_{len(tasks)}"

            # Create processing task
            task = _process_single_document(file_data, filename, processing_config)
            tasks.append(task)

        # Execute all tasks in parallel
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        for i, result in enumerate(batch_results):
            if isinstance(result, Exception):
                results.append({
                    "success": False,
                    "filename": files[i].filename or f"document_{i}",
                    "error": str(result)
                })
            else:
                results.append(result)

        processing_time_ms = (time.time() - start_time) * 1000

        logger.info(f"Batch processing completed: {len(results)} documents, {processing_time_ms:.1f}ms")

        return {
            "success": True,
            "batch_id": batch_id,
            "total_documents": len(files),
            "processing_time_ms": processing_time_ms,
            "results": results,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        processing_time_ms = (time.time() - start_time) * 1000
        error_message = f"Batch processing error: {str(e)}"

        logger.error(f"Batch processing failed: {error_message}")

        return {
            "success": False,
            "batch_id": batch_id,
            "error": error_message,
            "processing_time_ms": processing_time_ms,
            "timestamp": datetime.now().isoformat()
        }

async def _process_single_document(file_data: bytes, filename: str, config: ProcessingConfig) -> Dict[str, Any]:
    """Helper function to process a single document in batch mode"""

    try:
        # Use the same processing logic as the main endpoint
        format_info = await universal_format_engine.detect_format(file_data, filename)
        extraction_result = await universal_format_engine.extract_content(file_data, filename)

        # Simplified processing for batch mode
        if extraction_result.requires_ocr and config.llm_routing_enabled:
            document_analysis = await intelligent_router.analyze_document(file_data, filename)
            processing_strategy = await intelligent_router.create_processing_strategy(document_analysis)
            google_ai_result = await google_ai_hub.process_document(file_data, processing_strategy, filename)

            if google_ai_result and google_ai_result.success:
                return {
                    "success": True,
                    "filename": filename,
                    "extracted_text": google_ai_result.text,
                    "confidence": google_ai_result.confidence,
                    "processor_used": google_ai_result.processor_used,
                    "document_type": document_analysis.document_type
                }

        # Direct extraction result
        return {
            "success": extraction_result.success,
            "filename": filename,
            "extracted_text": extraction_result.text,
            "confidence": 0.95 if extraction_result.success else 0.0,
            "processor_used": "direct_extraction",
            "document_type": format_info.format_type
        }

    except Exception as e:
        return {
            "success": False,
            "filename": filename,
            "error": str(e)
        }

# Additional utility endpoints

@app.post("/api/v1/clear-cache", tags=["Utilities"])
async def clear_cache():
    """
    Clear processing cache

    Clears the intelligent routing cache to free memory
    and ensure fresh analysis for subsequent requests.
    """

    try:
        intelligent_router.clear_cache()
        return {
            "success": True,
            "message": "Cache cleared successfully",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.get("/api/v1/supported-formats", tags=["Configuration"])
async def get_supported_formats():
    """
    Get detailed information about supported file formats

    Returns comprehensive information about all supported formats
    including processing methods and capabilities.
    """

    format_categories = {
        "images": ["png", "jpg", "jpeg", "gif", "bmp", "tiff", "webp", "svg"],
        "documents": ["pdf"],
        "office": ["docx", "doc", "xlsx", "xls", "pptx", "ppt", "pptm"],
        "text": ["txt", "csv", "json", "yaml", "yml", "rtf"],
        "web": ["html", "htm", "xml", "mhtml"],
        "email": ["msg", "eml"],
        "archives": ["zip", "rar", "7z", "tar", "gz"],
        "business_intelligence": ["pbix", "pbit"],
        "open_office": ["odt", "ods", "odp"]
    }

    return {
        "total_formats": len(settings.SUPPORTED_FORMATS),
        "supported_formats": settings.SUPPORTED_FORMATS,
        "categories": format_categories,
        "processing_methods": {
            "direct_extraction": ["txt", "csv", "docx", "xlsx", "pptx", "html", "json"],
            "ocr_required": ["png", "jpg", "jpeg", "gif", "bmp", "tiff", "webp"],
            "hybrid_processing": ["pdf"],
            "archive_extraction": ["zip", "rar", "7z"]
        },
        "google_ai_processors": settings.GOOGLE_PROCESSORS
    }

@app.get("/api/v1/configuration-examples", tags=["Configuration"])
async def get_configuration_examples():
    """
    Get comprehensive configuration examples for different use cases

    Returns pre-configured examples for:
    - Speed-optimized processing
    - Quality-optimized processing
    - Direct OCR engine usage
    - Minimal processing
    - Custom preprocessing

    Each example includes detailed explanations of when to use each configuration.
    """

    return {
        "examples": {
            "speed_optimized": {
                "config": ConfigurationExamples.SPEED_OPTIMIZED,
                "description": "Optimized for fastest processing with good quality",
                "use_cases": ["High-volume processing", "Real-time applications", "Simple documents"],
                "expected_speed": "50% faster than quality-optimized",
                "trade_offs": "Slightly reduced accuracy for complex documents"
            },
            "quality_optimized": {
                "config": ConfigurationExamples.QUALITY_OPTIMIZED,
                "description": "Maximum accuracy with comprehensive analysis",
                "use_cases": ["Complex documents", "Legal documents", "Critical accuracy requirements"],
                "expected_speed": "Baseline processing time",
                "trade_offs": "Slower processing for maximum quality"
            },
            "direct_google_ocr": {
                "config": ConfigurationExamples.DIRECT_GOOGLE_OCR,
                "description": "Direct Google Document AI processing without LLM routing",
                "use_cases": ["Known document types", "Consistent formats", "Cost optimization"],
                "expected_speed": "Fast, no LLM analysis overhead",
                "trade_offs": "No intelligent processor selection"
            },
            "direct_tesseract": {
                "config": ConfigurationExamples.DIRECT_TESSERACT,
                "description": "Direct Tesseract OCR processing",
                "use_cases": ["Simple text documents", "Offline processing", "Cost-sensitive applications"],
                "expected_speed": "Very fast, local processing",
                "trade_offs": "Lower accuracy than Google Document AI"
            },
            "minimal_processing": {
                "config": ConfigurationExamples.MINIMAL_PROCESSING,
                "description": "Minimal processing for fastest possible results",
                "use_cases": ["Simple text extraction", "High-volume batch processing", "Testing"],
                "expected_speed": "Fastest possible",
                "trade_offs": "No post-processing or preprocessing enhancements"
            }
        },
        "configuration_options": {
            "ocr_engines": [engine.value for engine in OCREngine],
            "google_processors": [processor.value for processor in GoogleProcessor],
            "post_processing_versions": [version.value for version in PostProcessingVersion],
            "preprocessing_levels": [level.value for level in PreprocessingLevel],
            "preprocessing_techniques": [technique.value for technique in PreprocessingTechnique],
            "tesseract_psm_modes": {
                "0": "Orientation and script detection (OSD) only",
                "1": "Automatic page segmentation with OSD",
                "2": "Automatic page segmentation, but no OSD, or OCR",
                "3": "Fully automatic page segmentation, but no OSD (Default)",
                "4": "Assume a single column of text of variable sizes",
                "5": "Assume a single uniform block of vertically aligned text",
                "6": "Assume a single uniform block of text",
                "7": "Treat the image as a single text line",
                "8": "Treat the image as a single word",
                "9": "Treat the image as a single word in a circle",
                "10": "Treat the image as a single character",
                "11": "Sparse text. Find as much text as possible in no particular order",
                "12": "Sparse text with OSD",
                "13": "Raw line. Treat the image as a single text line, bypassing hacks"
            }
        },
        "custom_examples": {
            "insurance_claims": {
                "config": {
                    "llm_routing_enabled": True,
                    "post_processing": "v1",
                    "preprocessing": "enhanced",
                    "insurance_optimization": True,
                    "debug_mode": True
                },
                "description": "Optimized for insurance claim documents"
            },
            "forms_processing": {
                "config": {
                    "llm_routing_enabled": False,
                    "ocr_engine": "google",
                    "google_processor": "FORM_PARSER_PROCESSOR",
                    "post_processing": "v2",
                    "preprocessing": "basic"
                },
                "description": "Optimized for structured forms"
            },
            "handwritten_documents": {
                "config": {
                    "llm_routing_enabled": True,
                    "post_processing": "v1",
                    "preprocessing": "aggressive",
                    "confidence_threshold": 0.7
                },
                "description": "Optimized for handwritten content"
            }
        }
    }

@app.get("/api/v1/configuration-schema", tags=["Configuration"])
async def get_configuration_schema():
    """
    Get complete configuration schema with detailed field documentation

    Returns the full ProcessingConfig schema including:
    - All available configuration parameters
    - Field types, validation rules, and constraints
    - Default values and example values
    - Detailed descriptions for each parameter
    - Enum options for choice fields

    This endpoint provides comprehensive documentation for building
    configuration objects programmatically.
    """

    # Get the Pydantic schema
    schema = ProcessingConfig.schema()

    # Enhance with additional documentation
    enhanced_schema = {
        "title": "OCR Processing Configuration Schema",
        "description": "Complete configuration schema for the OCR processing pipeline",
        "version": "2.0.0",
        "schema": schema,
        "field_categories": {
            "core_routing": {
                "description": "Core routing and processing control",
                "fields": ["llm_routing_enabled", "parallel_processing", "confidence_threshold", "enable_fallbacks", "debug_mode", "insurance_optimization"]
            },
            "direct_ocr": {
                "description": "Direct OCR engine selection (bypasses LLM routing)",
                "fields": ["ocr_engine", "google_processor", "tesseract_psm"]
            },
            "post_processing": {
                "description": "GPT-powered post-processing configuration",
                "fields": ["post_processing"]
            },
            "preprocessing": {
                "description": "Image preprocessing and enhancement",
                "fields": ["preprocessing", "preprocessing_force_level", "preprocessing_techniques"]
            }
        },
        "validation_rules": {
            "confidence_threshold": "Must be between 0.0 and 1.0",
            "tesseract_psm": "Must be between 0 and 13 (Tesseract PSM modes)",
            "ocr_engine": "Must be 'google' or 'tesseract'",
            "google_processor": "Must be one of: OCR_PROCESSOR, FORM_PARSER_PROCESSOR, INVOICE_PROCESSOR, LAYOUT_PARSER_PROCESSOR",
            "post_processing": "Must be 'v1', 'v2', or false",
            "preprocessing": "Must be 'auto', 'minimal', 'basic', 'enhanced', 'aggressive', or false"
        },
        "performance_notes": {
            "speed_optimization": "Use post_processing='v2' for 50% speed improvement",
            "quality_optimization": "Use post_processing='v1' and preprocessing='enhanced' for maximum accuracy",
            "cost_optimization": "Use llm_routing_enabled=false to reduce API costs",
            "debug_optimization": "Use debug_mode=true for detailed processing information"
        },
        "common_combinations": {
            "speed_focused": {
                "llm_routing_enabled": True,
                "post_processing": "v2",
                "preprocessing": "auto",
                "debug_mode": False
            },
            "quality_focused": {
                "llm_routing_enabled": True,
                "post_processing": "v1",
                "preprocessing": "enhanced",
                "debug_mode": True
            },
            "cost_focused": {
                "llm_routing_enabled": False,
                "ocr_engine": "google",
                "post_processing": "v2",
                "preprocessing": "basic"
            }
        }
    }

    return enhanced_schema

@app.get("/api/v1/request-response-examples", tags=["Configuration"])
async def get_request_response_examples():
    """
    Get comprehensive request and response examples

    Returns detailed examples of:
    - Complete request structures (multipart form data)
    - Full response objects with all fields
    - Different configuration scenarios
    - Error response examples
    - cURL and JavaScript examples

    Perfect for API integration and testing.
    """

    return {
        "request_examples": {
            "multipart_form_structure": {
                "description": "Standard multipart/form-data request structure",
                "content_type": "multipart/form-data",
                "fields": {
                    "file": {
                        "type": "binary",
                        "description": "Document file (required)",
                        "example": "insurance_claim.pdf"
                    },
                    "config": {
                        "type": "string (JSON)",
                        "description": "Processing configuration (optional)",
                        "example": '{"llm_routing_enabled": true, "post_processing": "v2"}'
                    }
                }
            },
            "curl_examples": {
                "basic_request": {
                    "description": "Basic request with default configuration",
                    "command": 'curl -X POST "http://localhost:8000/api/v1/extract-text" -F "file=@document.pdf"'
                },
                "speed_optimized": {
                    "description": "Speed-optimized configuration",
                    "command": 'curl -X POST "http://localhost:8000/api/v1/extract-text" -F "file=@document.pdf" -F \'config={"llm_routing_enabled": true, "post_processing": "v2", "preprocessing": "auto"}\''
                },
                "quality_optimized": {
                    "description": "Quality-optimized configuration",
                    "command": 'curl -X POST "http://localhost:8000/api/v1/extract-text" -F "file=@document.pdf" -F \'config={"llm_routing_enabled": true, "post_processing": "v1", "preprocessing": "enhanced", "debug_mode": true}\''
                },
                "direct_ocr": {
                    "description": "Direct OCR without LLM routing",
                    "command": 'curl -X POST "http://localhost:8000/api/v1/extract-text" -F "file=@document.pdf" -F \'config={"llm_routing_enabled": false, "ocr_engine": "google", "google_processor": "LAYOUT_PARSER_PROCESSOR"}\''
                }
            },
            "javascript_examples": {
                "fetch_api": {
                    "description": "Using Fetch API with FormData",
                    "code": """
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('config', JSON.stringify({
    llm_routing_enabled: true,
    post_processing: "v2",
    preprocessing: "auto",
    debug_mode: false
}));

const response = await fetch('/api/v1/extract-text', {
    method: 'POST',
    body: formData
});

const result = await response.json();
console.log('Extracted text:', result.extracted_text);
console.log('Confidence:', result.confidence);
                    """
                },
                "axios_example": {
                    "description": "Using Axios with multipart form data",
                    "code": """
const formData = new FormData();
formData.append('file', file);
formData.append('config', JSON.stringify({
    llm_routing_enabled: true,
    post_processing: "v2"
}));

const response = await axios.post('/api/v1/extract-text', formData, {
    headers: {
        'Content-Type': 'multipart/form-data'
    }
});

console.log(response.data);
                    """
                }
            }
        },
        "response_examples": {
            "successful_response": {
                "description": "Complete successful response with all fields",
                "example": {
                    "success": True,
                    "extracted_text": "INSURANCE CLAIM FORM\\nPolicy Number: 12345\\nClaimant: John Doe\\nDate of Loss: 2024-01-15\\n...",
                    "confidence": 0.92,
                    "document_type": "insurance_claim",
                    "processor_used": "LAYOUT_PARSER_PROCESSOR_+_gpt_postprocessing",
                    "processing_time_ms": 15420.5,
                    "entities": [
                        {"type": "PERSON", "text": "John Doe", "confidence": 0.95},
                        {"type": "DATE", "text": "2024-01-15", "confidence": 0.98}
                    ],
                    "tables": [],
                    "form_fields": [
                        {"field_name": "Policy Number", "field_value": "12345", "confidence": 0.96}
                    ],
                    "metadata": {
                        "request_id": "123e4567-e89b-12d3-a456-************",
                        "filename": "insurance_claim.pdf",
                        "file_size": 245760,
                        "processing_config": {
                            "llm_routing_enabled": True,
                            "post_processing": "v2"
                        }
                    },
                    "step_timings": {
                        "1_format_detection_and_extraction": 125.3,
                        "2_llm_routing_analysis": 1850.7,
                        "3_ocr_processing": 8420.5,
                        "4_gpt_postprocessing": 4905.2
                    }
                }
            },
            "error_response": {
                "description": "Error response structure",
                "example": {
                    "success": False,
                    "extracted_text": "",
                    "confidence": 0.0,
                    "error_message": "File format not supported: .xyz",
                    "processing_time_ms": 125.3,
                    "metadata": {
                        "request_id": "123e4567-e89b-12d3-a456-************",
                        "filename": "document.xyz",
                        "error_code": "UNSUPPORTED_FORMAT"
                    }
                }
            }
        }
    }

# Error handlers

@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Custom HTTP exception handler"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "error": exc.detail,
            "status_code": exc.status_code,
            "timestamp": datetime.now().isoformat()
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """General exception handler"""
    logger.error(f"Unhandled exception: {str(exc)}")
    logger.error(traceback.format_exc())

    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "Internal server error",
            "timestamp": datetime.now().isoformat()
        }
    )

# Startup and shutdown events

@app.on_event("startup")
async def startup_event():
    """Application startup event"""
    logger.info("Starting Zurich OCR Engine")
    logger.info(f"Supported formats: {len(settings.SUPPORTED_FORMATS)}")
    logger.info(f"LLM routing: {'enabled' if settings.LLM_ROUTING_ENABLED else 'disabled'}")
    logger.info(f"Google Document AI: {'enabled' if google_ai_hub.client else 'disabled'}")
    logger.info(f"Parallel processing: {'enabled' if settings.PARALLEL_PROCESSING else 'disabled'}")
    logger.info("System ready for OCR processing")

@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown event"""
    logger.info("Shutting down Zurich OCR Engine")
    intelligent_router.clear_cache()
    logger.info("Shutdown complete")

# Main application entry point
if __name__ == "__main__":
    uvicorn.run(
        "zurich_ocr.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
        access_log=True
    )
