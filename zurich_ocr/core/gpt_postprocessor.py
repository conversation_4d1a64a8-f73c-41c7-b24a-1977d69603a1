"""
🏆 Award-winning GPT Post-Processing Engine

High-speed, high-accuracy post-processing of Google Document AI output
Specialized for insurance documents with mixed content handling
"""

import logging
import asyncio
import json
import re
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from openai import AsyncOpenAI
from .config import settings

logger = logging.getLogger(__name__)

@dataclass
class ContentRegion:
    """Represents a detected content region in the document"""
    region_type: str  # 'text', 'handwriting', 'drawing', 'table'
    coordinates: Dict[str, float]  # x, y, width, height
    content: str  # extracted text (empty for drawings)
    confidence: float
    page_number: int

@dataclass
class ProcessedContent:
    """Final processed content with structured data"""
    structured_data: Dict[str, Any]
    content_regions: List[ContentRegion]
    drawings_metadata: List[Dict[str, Any]]
    processing_time: float
    confidence: float
    success: bool
    error_message: Optional[str] = None
    # Detailed sub-step timings
    substep_timings: Optional[Dict[str, float]] = None

class GPTPostProcessor:
    """
    🏆 Award-winning GPT post-processing engine
    
    Transforms raw Google Document AI output into clean, structured insurance data
    Handles mixed content (text + handwriting + drawings) with optimal speed
    """
    
    def __init__(self):
        self.client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY) if settings.OPENAI_API_KEY else None
        self.model = settings.GPT_POSTPROCESSING_MODEL
        self.temperature = settings.GPT_POSTPROCESSING_TEMPERATURE
        self.max_tokens = settings.GPT_POSTPROCESSING_MAX_TOKENS
        self.vision_model = settings.GPT_VISION_MODEL
        
    async def process_document(
        self, 
        raw_ocr_output: Dict[str, Any], 
        document_type: str = "insurance",
        enable_vision_analysis: bool = True
    ) -> ProcessedContent:
        """
        Main processing pipeline: Google Document AI → GPT Post-Processing
        
        Args:
            raw_ocr_output: Output from Google Document AI
            document_type: Type of document for specialized processing
            enable_vision_analysis: Whether to use GPT Vision for mixed content
            
        Returns:
            ProcessedContent: Clean, structured output
        """
        
        start_time = asyncio.get_event_loop().time()
        substep_timings = {}

        try:
            # Step 1: Analyze content regions (fast)
            logger.info("🔍 GPT Post-Processing Step 1: Analyzing content regions")
            step_start = asyncio.get_event_loop().time()
            content_regions = await self._analyze_content_regions(raw_ocr_output)
            substep_timings["1_content_region_analysis"] = (asyncio.get_event_loop().time() - step_start) * 1000
            logger.info(f"Content region analysis completed: {len(content_regions)} regions found in {substep_timings['1_content_region_analysis']:.1f}ms")

            # Step 2: Separate text content from drawings (parallel processing)
            logger.info("🔄 GPT Post-Processing Step 2: Separating content types")
            step_start = asyncio.get_event_loop().time()
            text_content, drawings_metadata = await self._separate_content_types(content_regions)
            substep_timings["2_content_separation"] = (asyncio.get_event_loop().time() - step_start) * 1000
            logger.info(f"Content separation completed: {len(text_content)} chars text, {len(drawings_metadata)} drawings in {substep_timings['2_content_separation']:.1f}ms")

            # Step 3: GPT post-processing of text content (high-speed)
            logger.info("GPT Post-Processing Step 3: GPT text processing")
            step_start = asyncio.get_event_loop().time()
            structured_data = await self._gpt_postprocess_text(text_content, document_type, substep_timings)
            substep_timings["3_gpt_text_processing"] = (asyncio.get_event_loop().time() - step_start) * 1000
            logger.info(f"GPT text processing completed in {substep_timings['3_gpt_text_processing']:.1f}ms")

            processing_time = asyncio.get_event_loop().time() - start_time
            
            return ProcessedContent(
                structured_data=structured_data,
                content_regions=content_regions,
                drawings_metadata=drawings_metadata,
                processing_time=processing_time,
                confidence=0.95,
                success=True,
                substep_timings=substep_timings
            )
            
        except Exception as e:
            logger.error(f"GPT post-processing failed: {str(e)}")
            processing_time = asyncio.get_event_loop().time() - start_time
            
            return ProcessedContent(
                structured_data={},
                content_regions=[],
                drawings_metadata=[],
                processing_time=processing_time,
                confidence=0.0,
                success=False,
                error_message=str(e),
                substep_timings=substep_timings if 'substep_timings' in locals() else {}
            )
    
    async def _analyze_content_regions(self, raw_ocr_output: Dict[str, Any]) -> List[ContentRegion]:
        """Analyze and classify content regions from Google Document AI output"""
        
        regions = []
        
        # Extract from Google Document AI structure
        if 'pages' in raw_ocr_output:
            for page_idx, page in enumerate(raw_ocr_output['pages']):
                # Process blocks (paragraphs, tables, etc.)
                if 'blocks' in page:
                    for block in page['blocks']:
                        region = await self._classify_block(block, page_idx)
                        if region:
                            regions.append(region)
                
                # Process form fields
                if 'formFields' in page:
                    for field in page['formFields']:
                        region = await self._classify_form_field(field, page_idx)
                        if region:
                            regions.append(region)
        
        # Fallback: process raw text if no structured data
        elif 'text' in raw_ocr_output:
            regions.append(ContentRegion(
                region_type='text',
                coordinates={'x': 0, 'y': 0, 'width': 100, 'height': 100},
                content=raw_ocr_output['text'],
                confidence=raw_ocr_output.get('confidence', 0.8),
                page_number=0
            ))
        
        return regions
    
    async def _classify_block(self, block: Dict[str, Any], page_number: int) -> Optional[ContentRegion]:
        """Classify a block from Google Document AI as text, handwriting, or drawing"""
        
        # Extract text content
        text_content = ""
        if 'layout' in block and 'textAnchor' in block['layout']:
            text_content = block['layout']['textAnchor'].get('content', '')
        
        # Get bounding box
        coordinates = {'x': 0, 'y': 0, 'width': 0, 'height': 0}
        if 'layout' in block and 'boundingPoly' in block['layout']:
            vertices = block['layout']['boundingPoly'].get('vertices', [])
            if vertices:
                coordinates = self._extract_coordinates(vertices)
        
        # Classify content type based on characteristics
        region_type = await self._detect_content_type(text_content, block)
        
        return ContentRegion(
            region_type=region_type,
            coordinates=coordinates,
            content=text_content,
            confidence=block.get('confidence', 0.8),
            page_number=page_number
        )
    
    async def _classify_form_field(self, field: Dict[str, Any], page_number: int) -> Optional[ContentRegion]:
        """Classify form fields from Google Document AI"""
        
        # Extract field name and value
        field_name = ""
        field_value = ""
        
        if 'fieldName' in field:
            field_name = field['fieldName'].get('textAnchor', {}).get('content', '')
        if 'fieldValue' in field:
            field_value = field['fieldValue'].get('textAnchor', {}).get('content', '')
        
        content = f"{field_name}: {field_value}".strip()
        
        # Get coordinates
        coordinates = {'x': 0, 'y': 0, 'width': 0, 'height': 0}
        if 'fieldValue' in field and 'boundingPoly' in field['fieldValue']:
            vertices = field['fieldValue']['boundingPoly'].get('vertices', [])
            if vertices:
                coordinates = self._extract_coordinates(vertices)
        
        return ContentRegion(
            region_type='form_field',
            coordinates=coordinates,
            content=content,
            confidence=field.get('confidence', 0.8),
            page_number=page_number
        )
    
    async def _detect_content_type(self, text_content: str, block: Dict[str, Any]) -> str:
        """Fast content type detection without LLM calls"""
        
        # Fast heuristics for content type detection
        if not text_content or len(text_content.strip()) < 3:
            return 'drawing'  # Likely a drawing or image
        
        # Check for handwriting indicators (OCR artifacts, irregular spacing)
        handwriting_indicators = [
            len(re.findall(r'[А-Я]{2,}', text_content)) > 0,  # Cyrillic artifacts
            len(re.findall(r'\s{3,}', text_content)) > 2,      # Irregular spacing
            len(re.findall(r'[^\w\s\.,!?-]', text_content)) > len(text_content) * 0.1  # Special chars
        ]
        
        if sum(handwriting_indicators) >= 2:
            return 'handwriting'
        
        # Check for table-like structure
        if '\t' in text_content or '|' in text_content or len(text_content.split('\n')) > 3:
            return 'table'
        
        return 'text'  # Default to regular text
    
    def _extract_coordinates(self, vertices: List[Dict[str, Any]]) -> Dict[str, float]:
        """Extract bounding box coordinates from vertices"""
        
        if not vertices:
            return {'x': 0, 'y': 0, 'width': 0, 'height': 0}
        
        x_coords = [v.get('x', 0) for v in vertices]
        y_coords = [v.get('y', 0) for v in vertices]
        
        return {
            'x': min(x_coords),
            'y': min(y_coords),
            'width': max(x_coords) - min(x_coords),
            'height': max(y_coords) - min(y_coords)
        }
    
    async def _separate_content_types(self, regions: List[ContentRegion]) -> Tuple[str, List[Dict[str, Any]]]:
        """Separate text content from drawings and create metadata"""
        
        text_parts = []
        drawings_metadata = []
        
        for region in regions:
            if region.region_type in ['text', 'handwriting', 'form_field', 'table']:
                if region.content.strip():
                    text_parts.append(f"[{region.region_type.upper()}] {region.content}")
            
            elif region.region_type == 'drawing':
                drawings_metadata.append({
                    'type': 'drawing',
                    'coordinates': region.coordinates,
                    'page_number': region.page_number,
                    'confidence': region.confidence,
                    'description': 'Hand-drawn content area'
                })
        
        combined_text = "\n\n".join(text_parts)
        return combined_text, drawings_metadata

    async def _gpt_postprocess_text(self, text_content: str, document_type: str, substep_timings: Dict[str, float] = None) -> Dict[str, Any]:
        """High-speed GPT post-processing of extracted text content"""

        if not self.client or not text_content.strip():
            logger.warning("GPT client not available or empty text content")
            return {"raw_text": text_content}

        if substep_timings is None:
            substep_timings = {}

        try:
            # Step 3a: Clean OCR artifacts (fast)
            logger.info(f"GPT Sub-step 3a: Cleaning OCR artifacts ({len(text_content)} chars)")
            step_start = asyncio.get_event_loop().time()
            cleaned_text = await self._clean_ocr_artifacts(text_content)
            substep_timings["3a_ocr_artifact_cleaning"] = (asyncio.get_event_loop().time() - step_start) * 1000
            logger.info(f"OCR cleaning completed: {len(cleaned_text)} chars output in {substep_timings['3a_ocr_artifact_cleaning']:.1f}ms")

            # Step 3b: Structure content based on document type
            logger.info(f"GPT Sub-step 3b: Structuring content for {document_type} document")
            step_start = asyncio.get_event_loop().time()
            structured_data = await self._structure_content(cleaned_text, document_type)
            substep_timings["3b_content_structuring"] = (asyncio.get_event_loop().time() - step_start) * 1000
            logger.info(f"Content structuring completed: {len(structured_data.keys())} fields extracted in {substep_timings['3b_content_structuring']:.1f}ms")

            return structured_data

        except Exception as e:
            logger.error(f"GPT text processing failed: {str(e)}")
            return {"raw_text": text_content, "error": str(e)}

    async def _clean_ocr_artifacts(self, text: str) -> str:
        """Fast OCR artifact cleaning with GPT"""

        logger.info(f"🧹 Starting OCR artifact cleaning for {len(text)} characters")

        system_prompt = """You are an OCR text cleaner. Fix OCR artifacts, correct spacing, and normalize text.

Rules:
1. Fix obvious OCR errors (ТИЗМЭТА → remove, weird spacing → normalize)
2. Correct grammar and punctuation
3. Preserve all names, addresses, dates, and numbers exactly
4. Keep original meaning and structure
5. Return only the cleaned text, no explanations"""

        try:
            # Truncate text for speed if too long
            input_text = text[:2000] if len(text) > 2000 else text
            logger.info(f"📝 Sending {len(input_text)} characters to GPT for cleaning (model: {self.model})")

            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Clean this OCR text:\n\n{input_text}"}
                ],
                temperature=self.temperature,
                max_tokens=min(self.max_tokens, len(input_text) + 500)
            )

            cleaned_text = response.choices[0].message.content.strip()
            logger.info(f"OCR cleaning successful: {len(text)} → {len(cleaned_text)} characters")
            return cleaned_text

        except Exception as e:
            logger.warning(f"OCR cleaning failed: {str(e)}")
            return text  # Return original if cleaning fails

    async def _structure_content(self, cleaned_text: str, document_type: str) -> Dict[str, Any]:
        """Structure cleaned text into JSON based on document type"""

        logger.info(f"📊 Starting content structuring for {document_type} document ({len(cleaned_text)} chars)")

        # Get document-specific prompt
        system_prompt = self._get_structuring_prompt(document_type)
        logger.info(f"Using {document_type} document template for structuring")

        try:
            logger.info(f"Sending to GPT for structuring (model: {self.model}, temp: {self.temperature})")
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": cleaned_text}
                ],
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                response_format={"type": "json_object"}
            )

            structured_data = json.loads(response.choices[0].message.content)
            structured_data["original_text"] = cleaned_text

            logger.info(f"Content structuring successful: {len(structured_data.keys())} fields extracted")
            logger.info(f"Extracted fields: {list(structured_data.keys())}")

            return structured_data

        except Exception as e:
            logger.warning(f"Text structuring failed: {str(e)}")
            return {
                "original_text": cleaned_text,
                "error": str(e),
                "fallback_processing": True
            }

    def _get_structuring_prompt(self, document_type: str) -> str:
        """Get specialized prompt based on document type"""

        base_prompt = """You are an insurance document processor. Extract and structure the key information from the text.

Return valid JSON with relevant fields. Preserve all names, dates, amounts, and addresses exactly as written."""

        prompts = {
            "insurance": base_prompt + """

For insurance documents, extract:
{
  "document_type": "claim/policy/application",
  "incident_date": "date if found",
  "incident_description": "main incident summary",
  "claimant_info": {
    "name": "if found",
    "contact": "if found"
  },
  "claim_details": {
    "amount": "if found",
    "damage_description": "if found"
  },
  "key_facts": ["list of important facts"],
  "entities": {
    "dates": ["all dates found"],
    "amounts": ["all monetary amounts"],
    "locations": ["all addresses/locations"]
  }
}""",

            "financial": base_prompt + """

For financial documents, extract:
{
  "document_type": "invoice/receipt/statement",
  "transaction_date": "date if found",
  "vendor_info": {
    "name": "vendor name",
    "address": "if found"
  },
  "line_items": [
    {"description": "item", "amount": "price"}
  ],
  "totals": {
    "subtotal": "if found",
    "tax": "if found",
    "total": "final amount"
  },
  "payment_info": "payment method/details if found"
}""",

            "legal": base_prompt + """

For legal documents, extract:
{
  "document_type": "contract/agreement/statement",
  "parties": ["all parties mentioned"],
  "key_terms": ["important terms and conditions"],
  "dates": ["all relevant dates"],
  "obligations": ["key obligations or requirements"],
  "summary": "brief document summary"
}"""
        }

        return prompts.get(document_type, prompts["insurance"])

# Global instance
gpt_postprocessor = GPTPostProcessor()
