"""
🏆 Mixed Content Analyzer for Documents with Text + Handwriting + Drawings

High-speed analysis of mixed content using GPT Vision
Optimized for insurance documents with sketches and diagrams
"""

import logging
import asyncio
import json
import base64
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from openai import AsyncOpenAI
from .config import settings

logger = logging.getLogger(__name__)

@dataclass
class ContentAnalysis:
    """Analysis result for mixed content document"""
    content_regions: List[Dict[str, Any]]
    processing_strategy: Dict[str, str]
    has_drawings: bool
    has_handwriting: bool
    has_tables: bool
    confidence: float
    analysis_time: float

class MixedContentAnalyzer:
    """
    🏆 Award-winning mixed content analyzer
    
    Uses GPT Vision to analyze documents and identify:
    - Text regions (for OCR)
    - Handwriting regions (for specialized OCR)
    - Drawing/sketch regions (for metadata only)
    - Table regions (for structured extraction)
    """
    
    def __init__(self):
        self.client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY) if settings.OPENAI_API_KEY else None
        self.vision_model = settings.GPT_VISION_MODEL
        
    async def analyze_document_image(self, image_data: bytes, filename: str) -> ContentAnalysis:
        """
        Analyze document image to identify mixed content regions
        
        Args:
            image_data: Raw image bytes
            filename: Original filename
            
        Returns:
            ContentAnalysis: Detailed analysis of content regions
        """
        
        start_time = asyncio.get_event_loop().time()
        
        if not self.client:
            return self._create_fallback_analysis(start_time)
        
        try:
            # Convert image to base64 for GPT Vision
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # Analyze with GPT Vision
            analysis_result = await self._vision_analyze(image_base64)
            
            # Process analysis result
            content_regions = self._parse_content_regions(analysis_result)
            processing_strategy = self._create_processing_strategy(content_regions)
            
            analysis_time = asyncio.get_event_loop().time() - start_time
            
            return ContentAnalysis(
                content_regions=content_regions,
                processing_strategy=processing_strategy,
                has_drawings=any(r['type'] == 'drawing' for r in content_regions),
                has_handwriting=any(r['type'] == 'handwriting' for r in content_regions),
                has_tables=any(r['type'] == 'table' for r in content_regions),
                confidence=analysis_result.get('confidence', 0.85),
                analysis_time=analysis_time
            )
            
        except Exception as e:
            logger.error(f"Mixed content analysis failed: {str(e)}")
            return self._create_fallback_analysis(start_time, str(e))
    
    async def _vision_analyze(self, image_base64: str) -> Dict[str, Any]:
        """Use GPT Vision to analyze document content"""
        
        system_prompt = """You are a document content analyzer. Analyze this document image and identify different content regions.

Identify and locate:
1. PRINTED TEXT areas (typed/printed text)
2. HANDWRITTEN areas (handwritten notes, signatures)
3. DRAWING/SKETCH areas (diagrams, drawings, sketches)
4. TABLE areas (structured data in rows/columns)
5. FORM FIELDS (boxes, checkboxes, form elements)

For each region, provide:
- Type (text/handwriting/drawing/table/form)
- Location (approximate coordinates as percentages)
- Description
- Processing recommendation

Return JSON format:
{
  "regions": [
    {
      "type": "text|handwriting|drawing|table|form",
      "location": {"x": 0-100, "y": 0-100, "width": 0-100, "height": 0-100},
      "description": "brief description",
      "processing": "ocr|handwriting_ocr|skip|table_extraction"
    }
  ],
  "summary": "overall document description",
  "confidence": 0.0-1.0
}"""

        try:
            response = await self.client.chat.completions.create(
                model=self.vision_model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "Analyze this document and identify all content regions:"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{image_base64}",
                                    "detail": "high"
                                }
                            }
                        ]
                    }
                ],
                temperature=0.1,  # Low temperature for consistent analysis
                max_tokens=2000,
                response_format={"type": "json_object"}
            )
            
            return json.loads(response.choices[0].message.content)
            
        except Exception as e:
            logger.error(f"GPT Vision analysis failed: {str(e)}")
            raise
    
    def _parse_content_regions(self, analysis_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Parse GPT Vision analysis into standardized content regions"""
        
        regions = []
        
        for region in analysis_result.get('regions', []):
            # Standardize region format
            standardized_region = {
                'type': region.get('type', 'text'),
                'coordinates': region.get('location', {}),
                'description': region.get('description', ''),
                'processing_method': region.get('processing', 'ocr'),
                'confidence': 0.85  # Default confidence for vision analysis
            }
            
            regions.append(standardized_region)
        
        # If no regions found, create default text region
        if not regions:
            regions.append({
                'type': 'text',
                'coordinates': {'x': 0, 'y': 0, 'width': 100, 'height': 100},
                'description': 'Full document text content',
                'processing_method': 'ocr',
                'confidence': 0.8
            })
        
        return regions
    
    def _create_processing_strategy(self, content_regions: List[Dict[str, Any]]) -> Dict[str, str]:
        """Create optimal processing strategy based on content analysis"""
        
        strategy = {
            'primary_method': 'google_document_ai',
            'processors_needed': [],
            'vision_preprocessing': False,
            'drawing_handling': 'metadata_only'
        }
        
        # Determine processors needed based on content types
        content_types = [region['type'] for region in content_regions]
        
        if 'table' in content_types:
            strategy['processors_needed'].append('FORM_PARSER_PROCESSOR')
        
        if 'handwriting' in content_types:
            strategy['processors_needed'].append('OCR_PROCESSOR')
        
        if 'form' in content_types:
            strategy['processors_needed'].append('FORM_PARSER_PROCESSOR')
        
        if 'text' in content_types:
            strategy['processors_needed'].append('LAYOUT_PARSER_PROCESSOR')
        
        # Default to OCR if no specific processors identified
        if not strategy['processors_needed']:
            strategy['processors_needed'].append('OCR_PROCESSOR')
        
        # Enable vision preprocessing if drawings are present
        if 'drawing' in content_types:
            strategy['vision_preprocessing'] = True
            strategy['drawing_handling'] = 'extract_metadata'
        
        return strategy
    
    def _create_fallback_analysis(self, start_time: float, error_msg: str = None) -> ContentAnalysis:
        """Create fallback analysis when vision analysis fails"""
        
        analysis_time = asyncio.get_event_loop().time() - start_time
        
        # Default to treating entire document as text
        fallback_regions = [{
            'type': 'text',
            'coordinates': {'x': 0, 'y': 0, 'width': 100, 'height': 100},
            'description': 'Full document content (fallback analysis)',
            'processing_method': 'ocr',
            'confidence': 0.7
        }]
        
        fallback_strategy = {
            'primary_method': 'google_document_ai',
            'processors_needed': ['OCR_PROCESSOR'],
            'vision_preprocessing': False,
            'drawing_handling': 'metadata_only'
        }
        
        return ContentAnalysis(
            content_regions=fallback_regions,
            processing_strategy=fallback_strategy,
            has_drawings=False,
            has_handwriting=False,
            has_tables=False,
            confidence=0.7,
            analysis_time=analysis_time
        )
    
    async def should_use_vision_analysis(self, file_size: int, file_extension: str) -> bool:
        """Determine if vision analysis is worth the cost/time"""
        
        # Use vision analysis for:
        # 1. Image files (PNG, JPG) - likely to have mixed content
        # 2. Larger files - more likely to have complex layouts
        # 3. Skip for simple text files
        
        if file_extension.lower() in ['png', 'jpg', 'jpeg', 'tiff', 'bmp']:
            return True
        
        if file_extension.lower() == 'pdf' and file_size > 1024 * 1024:  # > 1MB
            return True
        
        return False

# Global instance
mixed_content_analyzer = MixedContentAnalyzer()
