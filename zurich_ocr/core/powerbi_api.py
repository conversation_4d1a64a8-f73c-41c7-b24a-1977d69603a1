"""
🏆 PowerBI REST API Integration

Optional PowerBI API integration for advanced PBIX processing.
Requires Microsoft Azure App Registration and PowerBI Pro/Premium license.
"""

import logging
import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import os

logger = logging.getLogger(__name__)

@dataclass
class PowerBIConfig:
    """PowerBI API configuration"""
    tenant_id: str
    client_id: str
    client_secret: str
    username: Optional[str] = None
    password: Optional[str] = None

@dataclass
class PowerBIDataset:
    """PowerBI dataset information"""
    id: str
    name: str
    tables: List[str]
    web_url: str

class PowerBIAPIClient:
    """
    🏆 PowerBI REST API client for PBIX data extraction
    
    Features:
    - Upload PBIX files to PowerBI Service
    - Extract data from datasets
    - Export reports as Excel/PDF
    - Manage workspaces and datasets
    """
    
    def __init__(self, config: PowerBIConfig):
        self.config = config
        self.access_token = None
        self.base_url = "https://api.powerbi.com/v1.0/myorg"
        
    async def authenticate(self) -> bool:
        """Authenticate with PowerBI API"""
        try:
            auth_url = f"https://login.microsoftonline.com/{self.config.tenant_id}/oauth2/v2.0/token"
            
            data = {
                'grant_type': 'client_credentials',
                'client_id': self.config.client_id,
                'client_secret': self.config.client_secret,
                'scope': 'https://analysis.windows.net/powerbi/api/.default'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(auth_url, data=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        self.access_token = result.get('access_token')
                        return True
                    else:
                        logger.error(f"Authentication failed: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            return False
    
    async def upload_pbix(self, pbix_data: bytes, dataset_name: str, workspace_id: Optional[str] = None) -> Optional[str]:
        """
        Upload PBIX file to PowerBI Service
        
        Args:
            pbix_data: PBIX file bytes
            dataset_name: Name for the dataset
            workspace_id: Optional workspace ID
            
        Returns:
            Dataset ID if successful
        """
        
        if not self.access_token:
            if not await self.authenticate():
                return None
        
        try:
            # Determine upload URL
            if workspace_id:
                upload_url = f"{self.base_url}/groups/{workspace_id}/imports"
            else:
                upload_url = f"{self.base_url}/imports"
            
            # Prepare multipart form data
            data = aiohttp.FormData()
            data.add_field('file', pbix_data, filename=f"{dataset_name}.pbix", content_type='application/octet-stream')
            data.add_field('datasetDisplayName', dataset_name)
            
            headers = {
                'Authorization': f'Bearer {self.access_token}'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(upload_url, data=data, headers=headers) as response:
                    if response.status == 202:
                        result = await response.json()
                        import_id = result.get('id')
                        
                        # Wait for import to complete
                        dataset_id = await self._wait_for_import(import_id, workspace_id)
                        return dataset_id
                    else:
                        logger.error(f"Upload failed: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"Upload error: {str(e)}")
            return None
    
    async def export_dataset_data(self, dataset_id: str, table_name: str, workspace_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Export data from PowerBI dataset table
        
        Args:
            dataset_id: Dataset ID
            table_name: Table name to export
            workspace_id: Optional workspace ID
            
        Returns:
            Table data as dictionary
        """
        
        if not self.access_token:
            if not await self.authenticate():
                return None
        
        try:
            # Build query URL
            if workspace_id:
                query_url = f"{self.base_url}/groups/{workspace_id}/datasets/{dataset_id}/executeQueries"
            else:
                query_url = f"{self.base_url}/datasets/{dataset_id}/executeQueries"
            
            # DAX query to get table data
            dax_query = f"EVALUATE TOPN(1000, '{table_name}')"
            
            query_data = {
                "queries": [
                    {
                        "query": dax_query
                    }
                ]
            }
            
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(query_url, json=query_data, headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result
                    else:
                        logger.error(f"Query failed: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"Export error: {str(e)}")
            return None
    
    async def get_dataset_info(self, dataset_id: str, workspace_id: Optional[str] = None) -> Optional[PowerBIDataset]:
        """Get dataset information including tables"""
        
        if not self.access_token:
            if not await self.authenticate():
                return None
        
        try:
            # Get dataset metadata
            if workspace_id:
                dataset_url = f"{self.base_url}/groups/{workspace_id}/datasets/{dataset_id}"
                tables_url = f"{self.base_url}/groups/{workspace_id}/datasets/{dataset_id}/tables"
            else:
                dataset_url = f"{self.base_url}/datasets/{dataset_id}"
                tables_url = f"{self.base_url}/datasets/{dataset_id}/tables"
            
            headers = {
                'Authorization': f'Bearer {self.access_token}'
            }
            
            async with aiohttp.ClientSession() as session:
                # Get dataset info
                async with session.get(dataset_url, headers=headers) as response:
                    if response.status != 200:
                        return None
                    dataset_info = await response.json()
                
                # Get tables info
                async with session.get(tables_url, headers=headers) as response:
                    if response.status != 200:
                        return None
                    tables_info = await response.json()
                
                table_names = [table['name'] for table in tables_info.get('value', [])]
                
                return PowerBIDataset(
                    id=dataset_info['id'],
                    name=dataset_info['name'],
                    tables=table_names,
                    web_url=dataset_info.get('webUrl', '')
                )
                
        except Exception as e:
            logger.error(f"Get dataset info error: {str(e)}")
            return None
    
    async def _wait_for_import(self, import_id: str, workspace_id: Optional[str] = None, max_wait: int = 300) -> Optional[str]:
        """Wait for PBIX import to complete"""
        
        if workspace_id:
            status_url = f"{self.base_url}/groups/{workspace_id}/imports/{import_id}"
        else:
            status_url = f"{self.base_url}/imports/{import_id}"
        
        headers = {
            'Authorization': f'Bearer {self.access_token}'
        }
        
        for _ in range(max_wait // 5):  # Check every 5 seconds
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(status_url, headers=headers) as response:
                        if response.status == 200:
                            result = await response.json()
                            import_state = result.get('importState')
                            
                            if import_state == 'Succeeded':
                                # Get dataset ID from reports or datasets
                                datasets = result.get('datasets', [])
                                if datasets:
                                    return datasets[0]['id']
                                return None
                            elif import_state == 'Failed':
                                logger.error(f"Import failed: {result.get('error', 'Unknown error')}")
                                return None
                            
                await asyncio.sleep(5)
                
            except Exception as e:
                logger.error(f"Import status check error: {str(e)}")
                return None
        
        logger.error("Import timeout")
        return None

def create_powerbi_client() -> Optional[PowerBIAPIClient]:
    """Create PowerBI client from environment variables"""
    
    tenant_id = os.getenv('POWERBI_TENANT_ID')
    client_id = os.getenv('POWERBI_CLIENT_ID')
    client_secret = os.getenv('POWERBI_CLIENT_SECRET')
    
    if not all([tenant_id, client_id, client_secret]):
        logger.info("PowerBI API credentials not configured - using basic PBIX extraction")
        return None
    
    config = PowerBIConfig(
        tenant_id=tenant_id,
        client_id=client_id,
        client_secret=client_secret
    )
    
    return PowerBIAPIClient(config)
