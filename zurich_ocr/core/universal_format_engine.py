"""
🏆 Award-Winning Universal File Format Support Engine
Comprehensive file format detection and processing for all document types
"""

import io
import os
import zipfile
import tempfile
import logging
from typing import Dict, Any, List, Tuple, Optional, Union
from dataclasses import dataclass
from pathlib import Path
import magic
import asyncio

# Office document processing
from docx import Document as DocxDocument
from openpyxl import load_workbook
from pptx import Presentation
import pandas as pd

# Email processing
import email
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

# Web content processing
from bs4 import BeautifulSoup

# PDF processing
import pdfplumber
from pdf2image import convert_from_bytes

# Image processing
from PIL import Image

from .config import settings

logger = logging.getLogger(__name__)

@dataclass
class FileFormatInfo:
    """Information about detected file format"""
    format_type: str
    mime_type: str
    extension: str
    requires_ocr: bool
    supports_direct_extraction: bool
    complexity_level: str
    estimated_pages: int
    file_size: int

@dataclass
class ExtractionResult:
    """Result from file format extraction"""
    success: bool
    text: str
    structured_data: Dict[str, Any]
    metadata: Dict[str, Any]
    requires_ocr: bool
    extracted_files: List[Tuple[str, bytes]] = None  # For archives
    error_message: Optional[str] = None

class UniversalFormatEngine:
    """
    🏆 Award-winning universal file format support engine
    
    Handles all file formats with intelligent routing to appropriate processing methods.
    Supports direct extraction where possible, OCR routing where necessary.
    """
    
    def __init__(self):
        self.supported_formats = set(settings.SUPPORTED_FORMATS)
        self.format_handlers = self._initialize_format_handlers()
        
    def _initialize_format_handlers(self) -> Dict[str, callable]:
        """Initialize format-specific handlers"""
        return {
            # Image formats - require OCR
            'png': self._process_image,
            'jpg': self._process_image,
            'jpeg': self._process_image,
            'gif': self._process_image,
            'bmp': self._process_image,
            'tiff': self._process_image,
            'webp': self._process_image,
            'svg': self._process_image,
            
            # PDF - hybrid processing
            'pdf': self._process_pdf,
            
            # Microsoft Office - direct extraction
            'docx': self._process_docx,
            'doc': self._process_doc,
            'xlsx': self._process_xlsx,
            'xls': self._process_xls,
            'pptx': self._process_pptx,
            'ppt': self._process_ppt,
            'pptm': self._process_pptx,
            
            # Email formats
            'msg': self._process_msg,
            'eml': self._process_eml,
            
            # Web formats
            'html': self._process_html,
            'htm': self._process_html,
            'xml': self._process_xml,
            'mhtml': self._process_mhtml,
            
            # Text and data formats
            'txt': self._process_text,
            'csv': self._process_csv,
            'json': self._process_json,
            'yaml': self._process_yaml,
            'yml': self._process_yaml,
            'rtf': self._process_rtf,
            
            # Open Office formats
            'odt': self._process_odt,
            'ods': self._process_ods,
            'odp': self._process_odp,
            
            # Archive formats
            'zip': self._process_zip,
            'rar': self._process_rar,
            '7z': self._process_7z,
            'tar': self._process_tar,
            'gz': self._process_gz,
            
            # Business Intelligence
            'pbix': self._process_pbix,
            'pbit': self._process_pbix,
        }
    
    async def detect_format(self, file_data: bytes, filename: str) -> FileFormatInfo:
        """
        Detect file format and provide comprehensive information
        
        Args:
            file_data: Raw file bytes
            filename: Original filename
            
        Returns:
            FileFormatInfo: Comprehensive format information
        """
        
        # Extract extension
        extension = filename.split('.')[-1].lower() if '.' in filename else ""
        
        # Detect MIME type
        try:
            mime_type = magic.from_buffer(file_data, mime=True)
        except:
            mime_type = self._guess_mime_type(file_data, extension)
        
        # Determine format type
        format_type = self._categorize_format(extension, mime_type)
        
        # Check if OCR is required
        requires_ocr = self._requires_ocr(extension, mime_type)
        
        # Check if direct extraction is supported
        supports_direct_extraction = extension in self.format_handlers
        
        # Estimate complexity
        complexity_level = self._estimate_complexity(file_data, extension, mime_type)
        
        # Estimate page count
        estimated_pages = await self._estimate_page_count(file_data, extension)
        
        return FileFormatInfo(
            format_type=format_type,
            mime_type=mime_type,
            extension=extension,
            requires_ocr=requires_ocr,
            supports_direct_extraction=supports_direct_extraction,
            complexity_level=complexity_level,
            estimated_pages=estimated_pages,
            file_size=len(file_data)
        )
    
    async def extract_content(self, file_data: bytes, filename: str) -> ExtractionResult:
        """
        Extract content from file using appropriate method
        
        Args:
            file_data: Raw file bytes
            filename: Original filename
            
        Returns:
            ExtractionResult: Extraction result with content and metadata
        """
        
        try:
            # Detect format
            format_info = await self.detect_format(file_data, filename)
            
            # Check if format is supported
            if format_info.extension not in self.supported_formats:
                return ExtractionResult(
                    success=False,
                    text="",
                    structured_data={},
                    metadata={"format_info": format_info.__dict__},
                    requires_ocr=False,
                    error_message=f"Unsupported format: {format_info.extension}"
                )
            
            # Route to appropriate handler
            if format_info.supports_direct_extraction:
                handler = self.format_handlers[format_info.extension]
                result = await handler(file_data, filename, format_info)
            else:
                # Fallback to OCR processing
                result = ExtractionResult(
                    success=True,
                    text="",
                    structured_data={},
                    metadata={"format_info": format_info.__dict__},
                    requires_ocr=True
                )
            
            # Add format information to metadata
            result.metadata["format_info"] = format_info.__dict__
            
            return result
            
        except Exception as e:
            logger.error(f"Error extracting content from {filename}: {str(e)}")
            return ExtractionResult(
                success=False,
                text="",
                structured_data={},
                metadata={"error": str(e)},
                requires_ocr=False,
                error_message=f"Extraction error: {str(e)}"
            )
    
    def _categorize_format(self, extension: str, mime_type: str) -> str:
        """Categorize file format into broad categories"""
        
        image_formats = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff', 'webp', 'svg'}
        office_formats = {'docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt', 'pptm'}
        text_formats = {'txt', 'csv', 'json', 'yaml', 'yml', 'rtf'}
        web_formats = {'html', 'htm', 'xml', 'mhtml'}
        email_formats = {'msg', 'eml'}
        archive_formats = {'zip', 'rar', '7z', 'tar', 'gz'}
        
        if extension in image_formats:
            return "image"
        elif extension == 'pdf':
            return "pdf"
        elif extension in office_formats:
            return "office"
        elif extension in text_formats:
            return "text"
        elif extension in web_formats:
            return "web"
        elif extension in email_formats:
            return "email"
        elif extension in archive_formats:
            return "archive"
        else:
            return "other"
    
    def _requires_ocr(self, extension: str, mime_type: str) -> bool:
        """Determine if file requires OCR processing"""
        
        # Image formats always require OCR
        if mime_type.startswith('image/'):
            return True
        
        # PDF might require OCR (determined during processing)
        if extension == 'pdf':
            return True  # Will be refined during PDF processing
        
        # All other formats support direct extraction
        return False
    
    def _estimate_complexity(self, file_data: bytes, extension: str, mime_type: str) -> str:
        """Estimate document complexity level"""
        
        file_size = len(file_data)
        
        # Size-based complexity estimation
        if file_size < 100 * 1024:  # < 100KB
            base_complexity = "low"
        elif file_size < 1024 * 1024:  # < 1MB
            base_complexity = "medium"
        else:
            base_complexity = "high"
        
        # Format-specific adjustments
        if extension in ['pdf', 'pptx', 'xlsx']:
            if base_complexity == "low":
                base_complexity = "medium"
        
        if extension in ['zip', 'rar', '7z']:
            base_complexity = "high"
        
        return base_complexity
    
    async def _estimate_page_count(self, file_data: bytes, extension: str) -> int:
        """Estimate number of pages in document"""
        
        try:
            if extension == 'pdf':
                with pdfplumber.open(io.BytesIO(file_data)) as pdf:
                    return len(pdf.pages)
            elif extension == 'pptx':
                prs = Presentation(io.BytesIO(file_data))
                return len(prs.slides)
            elif extension == 'docx':
                # Estimate based on file size (rough approximation)
                return max(1, len(file_data) // (50 * 1024))  # ~50KB per page
            else:
                return 1
        except:
            return 1
    
    def _guess_mime_type(self, file_data: bytes, extension: str) -> str:
        """Fallback MIME type detection"""
        
        mime_mapping = {
            'pdf': 'application/pdf',
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'gif': 'image/gif',
            'bmp': 'image/bmp',
            'tiff': 'image/tiff',
            'webp': 'image/webp',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'html': 'text/html',
            'txt': 'text/plain',
            'csv': 'text/csv',
            'json': 'application/json',
            'xml': 'application/xml',
            'zip': 'application/zip'
        }
        
        return mime_mapping.get(extension, 'application/octet-stream')

    # Format-specific processing methods

    async def _process_image(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        """Process image files with Tesseract OCR fallback"""
        try:
            # Try Tesseract OCR as fallback
            import pytesseract
            from PIL import Image
            import io

            # Open image
            image = Image.open(io.BytesIO(file_data))

            # Get image dimensions
            width, height = image.size

            # Perform OCR
            extracted_text = pytesseract.image_to_string(image, config='--psm 6')

            if extracted_text.strip():
                return ExtractionResult(
                    success=True,
                    text=extracted_text.strip(),
                    structured_data={
                        "image_info": {
                            "width": width,
                            "height": height,
                            "format": format_info.extension
                        }
                    },
                    metadata={"processing_method": "tesseract_ocr"},
                    requires_ocr=False  # Already processed
                )
            else:
                return ExtractionResult(
                    success=False,
                    text="",
                    structured_data={
                        "image_info": {
                            "width": width,
                            "height": height,
                            "format": format_info.extension
                        }
                    },
                    metadata={"processing_method": "tesseract_ocr_no_text"},
                    requires_ocr=True,
                    error_message="No text detected in image"
                )

        except Exception as e:
            logger.error(f"Error processing image {filename}: {str(e)}")
            return ExtractionResult(
                success=False,
                text="",
                structured_data={"image_info": {"width": 0, "height": 0, "format": format_info.extension}},
                metadata={"processing_method": "image_ocr_failed", "error": str(e)},
                requires_ocr=True,
                error_message=f"Image OCR failed: {str(e)}"
            )

    async def _process_pdf(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        """Process PDF files with hybrid text extraction and OCR"""
        try:
            text_content = ""
            pages_data = []
            requires_ocr = False

            with pdfplumber.open(io.BytesIO(file_data)) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    page_text = page.extract_text() or ""

                    # Extract tables
                    tables = page.extract_tables() or []

                    pages_data.append({
                        "page_number": page_num + 1,
                        "text": page_text,
                        "tables": tables,
                        "has_text": bool(page_text.strip())
                    })

                    text_content += page_text + "\n"

                    # If page has no extractable text, it needs OCR
                    if not page_text.strip():
                        requires_ocr = True

            return ExtractionResult(
                success=True,
                text=text_content.strip(),
                structured_data={"pages": pages_data, "total_pages": len(pages_data)},
                metadata={"processing_method": "pdf_hybrid", "extractable_text": bool(text_content.strip())},
                requires_ocr=requires_ocr
            )

        except Exception as e:
            logger.error(f"Error processing PDF {filename}: {str(e)}")
            return ExtractionResult(
                success=False,
                text="",
                structured_data={},
                metadata={"error": str(e)},
                requires_ocr=True,
                error_message=f"PDF processing error: {str(e)}"
            )

    async def _process_docx(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        """Process DOCX files with direct text extraction"""
        try:
            doc = DocxDocument(io.BytesIO(file_data))

            # Extract paragraphs
            paragraphs = [para.text for para in doc.paragraphs if para.text.strip()]
            text_content = "\n".join(paragraphs)

            # Extract tables
            tables_data = []
            for table in doc.tables:
                table_data = []
                for row in table.rows:
                    row_data = [cell.text.strip() for cell in row.cells]
                    table_data.append(row_data)
                tables_data.append(table_data)

            # Extract document properties
            properties = {
                "title": doc.core_properties.title or "",
                "author": doc.core_properties.author or "",
                "subject": doc.core_properties.subject or "",
                "created": str(doc.core_properties.created) if doc.core_properties.created else "",
                "modified": str(doc.core_properties.modified) if doc.core_properties.modified else ""
            }

            return ExtractionResult(
                success=True,
                text=text_content,
                structured_data={
                    "paragraphs": paragraphs,
                    "tables": tables_data,
                    "properties": properties
                },
                metadata={"processing_method": "docx_direct"},
                requires_ocr=False
            )

        except Exception as e:
            logger.error(f"Error processing DOCX {filename}: {str(e)}")
            return ExtractionResult(
                success=False,
                text="",
                structured_data={},
                metadata={"error": str(e)},
                requires_ocr=False,
                error_message=f"DOCX processing error: {str(e)}"
            )

    async def _process_xlsx(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        """Process XLSX files with structured data extraction"""
        try:
            workbook = load_workbook(io.BytesIO(file_data), data_only=True)

            sheets_data = {}
            all_text = []

            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]

                # Extract data as list of lists
                sheet_data = []
                for row in sheet.iter_rows(values_only=True):
                    if any(cell is not None for cell in row):  # Skip empty rows
                        row_data = [str(cell) if cell is not None else "" for cell in row]
                        sheet_data.append(row_data)
                        all_text.extend([str(cell) for cell in row if cell is not None])

                sheets_data[sheet_name] = sheet_data

            text_content = " ".join(all_text)

            return ExtractionResult(
                success=True,
                text=text_content,
                structured_data={
                    "sheets": sheets_data,
                    "sheet_names": list(workbook.sheetnames)
                },
                metadata={"processing_method": "xlsx_direct", "sheet_count": len(workbook.sheetnames)},
                requires_ocr=False
            )

        except Exception as e:
            logger.error(f"Error processing XLSX {filename}: {str(e)}")
            return ExtractionResult(
                success=False,
                text="",
                structured_data={},
                metadata={"error": str(e)},
                requires_ocr=False,
                error_message=f"XLSX processing error: {str(e)}"
            )

    async def _process_pptx(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        """Process PPTX files with slide content extraction"""
        try:
            prs = Presentation(io.BytesIO(file_data))

            slides_data = []
            all_text = []

            for slide_num, slide in enumerate(prs.slides):
                slide_text = []

                # Extract text from shapes
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        slide_text.append(shape.text.strip())

                slide_content = "\n".join(slide_text)
                slides_data.append({
                    "slide_number": slide_num + 1,
                    "text": slide_content,
                    "text_elements": slide_text
                })

                all_text.append(slide_content)

            text_content = "\n\n".join(all_text)

            return ExtractionResult(
                success=True,
                text=text_content,
                structured_data={
                    "slides": slides_data,
                    "slide_count": len(slides_data)
                },
                metadata={"processing_method": "pptx_direct"},
                requires_ocr=False
            )

        except Exception as e:
            logger.error(f"Error processing PPTX {filename}: {str(e)}")
            return ExtractionResult(
                success=False,
                text="",
                structured_data={},
                metadata={"error": str(e)},
                requires_ocr=False,
                error_message=f"PPTX processing error: {str(e)}"
            )

    async def _process_text(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        """Process plain text files"""
        try:
            # Try different encodings
            encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1252']
            text_content = ""

            for encoding in encodings:
                try:
                    text_content = file_data.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue

            if not text_content:
                text_content = file_data.decode('utf-8', errors='replace')

            return ExtractionResult(
                success=True,
                text=text_content,
                structured_data={"line_count": len(text_content.split('\n'))},
                metadata={"processing_method": "text_direct"},
                requires_ocr=False
            )

        except Exception as e:
            return ExtractionResult(
                success=False,
                text="",
                structured_data={},
                metadata={"error": str(e)},
                requires_ocr=False,
                error_message=f"Text processing error: {str(e)}"
            )

    async def _process_csv(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        """Process CSV files with structured data extraction"""
        try:
            # Try to read as CSV
            df = pd.read_csv(io.BytesIO(file_data))

            # Convert to structured format
            data = df.to_dict('records')
            text_content = df.to_string()

            return ExtractionResult(
                success=True,
                text=text_content,
                structured_data={
                    "data": data,
                    "columns": list(df.columns),
                    "row_count": len(df),
                    "column_count": len(df.columns)
                },
                metadata={"processing_method": "csv_direct"},
                requires_ocr=False
            )

        except Exception as e:
            # Fallback to text processing
            return await self._process_text(file_data, filename, format_info)

    async def _process_html(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        """Process HTML files with clean text extraction"""
        try:
            html_content = file_data.decode('utf-8', errors='replace')
            soup = BeautifulSoup(html_content, 'html.parser')

            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()

            # Extract text
            text_content = soup.get_text()

            # Clean up text
            lines = (line.strip() for line in text_content.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text_content = ' '.join(chunk for chunk in chunks if chunk)

            # Extract metadata
            title = soup.title.string if soup.title else ""
            meta_description = ""
            if soup.find("meta", attrs={"name": "description"}):
                meta_description = soup.find("meta", attrs={"name": "description"}).get("content", "")

            return ExtractionResult(
                success=True,
                text=text_content,
                structured_data={
                    "title": title,
                    "meta_description": meta_description,
                    "raw_html": html_content[:1000]  # First 1000 chars
                },
                metadata={"processing_method": "html_direct"},
                requires_ocr=False
            )

        except Exception as e:
            return ExtractionResult(
                success=False,
                text="",
                structured_data={},
                metadata={"error": str(e)},
                requires_ocr=False,
                error_message=f"HTML processing error: {str(e)}"
            )

    async def _process_zip(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        """Process ZIP archives by extracting and processing contents"""
        try:
            extracted_files = []
            all_text = []

            with zipfile.ZipFile(io.BytesIO(file_data), 'r') as zip_file:
                for file_info in zip_file.filelist:
                    if not file_info.is_dir() and file_info.file_size < 10 * 1024 * 1024:  # Skip large files
                        try:
                            file_content = zip_file.read(file_info.filename)
                            extracted_files.append((file_info.filename, file_content))

                            # Try to extract text from supported formats
                            file_ext = file_info.filename.split('.')[-1].lower()
                            if file_ext in ['txt', 'csv', 'html', 'htm']:
                                try:
                                    text = file_content.decode('utf-8', errors='replace')
                                    all_text.append(f"=== {file_info.filename} ===\n{text}\n")
                                except:
                                    pass
                        except Exception as e:
                            logger.warning(f"Failed to extract {file_info.filename}: {str(e)}")

            return ExtractionResult(
                success=True,
                text="\n".join(all_text),
                structured_data={
                    "file_count": len(extracted_files),
                    "file_list": [name for name, _ in extracted_files]
                },
                metadata={"processing_method": "zip_extraction"},
                requires_ocr=len(all_text) == 0,  # Require OCR if no text extracted
                extracted_files=extracted_files
            )

        except Exception as e:
            return ExtractionResult(
                success=False,
                text="",
                structured_data={},
                metadata={"error": str(e)},
                requires_ocr=False,
                error_message=f"ZIP processing error: {str(e)}"
            )

    async def _process_pbix(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        """Process PBIX files with advanced extraction strategies"""
        try:
            from .pbix_processor import pbix_processor

            # Use specialized PBIX processor
            analysis = await pbix_processor.process_pbix(file_data, filename)

            if not analysis.success:
                return ExtractionResult(
                    success=False,
                    text="",
                    structured_data={},
                    metadata={"error": analysis.error_message},
                    requires_ocr=False,
                    error_message=f"PBIX processing error: {analysis.error_message}"
                )

            # Create comprehensive response
            return ExtractionResult(
                success=True,
                text=analysis.extracted_text,
                structured_data={
                    "file_type": "pbix",
                    "file_structure": analysis.file_structure,
                    "data_models": analysis.data_models,
                    "metadata_files": analysis.metadata_files,
                    "recommendations": analysis.recommendations,
                    "extraction_method": "advanced_pbix_analysis"
                },
                metadata={
                    "processing_method": "pbix_advanced_processor",
                    "format_info": format_info.__dict__,
                    "files_found": len(analysis.file_structure),
                    "data_models_found": len(analysis.data_models),
                    "metadata_files_found": len(analysis.metadata_files)
                },
                requires_ocr=False
            )

        except Exception as e:
            logger.error(f"Error processing PBIX {filename}: {str(e)}")
            return ExtractionResult(
                success=False,
                text="",
                structured_data={},
                metadata={"error": str(e)},
                requires_ocr=False,
                error_message=f"PBIX processing error: {str(e)}. Consider exporting as Excel/CSV format."
            )

    # Placeholder methods for other formats
    async def _process_doc(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        """Process legacy DOC files - requires OCR or conversion"""
        return ExtractionResult(
            success=True,
            text="",
            structured_data={},
            metadata={"processing_method": "doc_ocr_required"},
            requires_ocr=True
        )

    async def _process_xls(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        """Process legacy XLS files"""
        try:
            df = pd.read_excel(io.BytesIO(file_data))
            text_content = df.to_string()

            return ExtractionResult(
                success=True,
                text=text_content,
                structured_data={"data": df.to_dict('records')},
                metadata={"processing_method": "xls_direct"},
                requires_ocr=False
            )
        except:
            return ExtractionResult(
                success=True,
                text="",
                structured_data={},
                metadata={"processing_method": "xls_ocr_required"},
                requires_ocr=True
            )

    async def _process_ppt(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        """Process legacy PPT files - requires OCR"""
        return ExtractionResult(
            success=True,
            text="",
            structured_data={},
            metadata={"processing_method": "ppt_ocr_required"},
            requires_ocr=True
        )

    # Stub methods for other formats (implement as needed)
    async def _process_msg(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        return ExtractionResult(success=True, text="", structured_data={}, metadata={}, requires_ocr=True)

    async def _process_eml(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        return ExtractionResult(success=True, text="", structured_data={}, metadata={}, requires_ocr=True)

    async def _process_xml(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        return await self._process_text(file_data, filename, format_info)

    async def _process_json(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        return await self._process_text(file_data, filename, format_info)

    async def _process_yaml(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        return await self._process_text(file_data, filename, format_info)

    async def _process_rtf(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        return ExtractionResult(success=True, text="", structured_data={}, metadata={}, requires_ocr=True)

    async def _process_odt(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        return ExtractionResult(success=True, text="", structured_data={}, metadata={}, requires_ocr=True)

    async def _process_ods(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        return ExtractionResult(success=True, text="", structured_data={}, metadata={}, requires_ocr=True)

    async def _process_odp(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        return ExtractionResult(success=True, text="", structured_data={}, metadata={}, requires_ocr=True)

    async def _process_rar(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        return ExtractionResult(success=True, text="", structured_data={}, metadata={}, requires_ocr=True)

    async def _process_7z(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        return ExtractionResult(success=True, text="", structured_data={}, metadata={}, requires_ocr=True)

    async def _process_tar(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        return ExtractionResult(success=True, text="", structured_data={}, metadata={}, requires_ocr=True)

    async def _process_gz(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        return ExtractionResult(success=True, text="", structured_data={}, metadata={}, requires_ocr=True)



    async def _process_mhtml(self, file_data: bytes, filename: str, format_info: FileFormatInfo) -> ExtractionResult:
        return await self._process_html(file_data, filename, format_info)

# Global universal format engine instance
universal_format_engine = UniversalFormatEngine()
