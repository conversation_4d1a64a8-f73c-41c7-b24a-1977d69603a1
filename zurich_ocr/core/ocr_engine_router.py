"""
OCR Engine Router - Direct Engine Selection

Routes documents to specific OCR engines when LLM routing is disabled:
- Google Document AI (with processor selection)
- Tesseract OCR (with PSM configuration)
"""

import logging
import time
from typing import Dict, Any, Optional
from dataclasses import dataclass

from .google_document_ai_hub import google_ai_hub, ProcessingResult
from .config import settings

logger = logging.getLogger(__name__)

@dataclass
class OCREngineResult:
    """Result from direct OCR engine processing"""
    success: bool
    text: str
    confidence: float
    processor_used: str
    processing_time: float
    engine_used: str
    error_message: Optional[str] = None

class OCREngineRouter:
    """
    🎯 Direct OCR Engine Router
    
    Routes documents to specific OCR engines without LLM analysis:
    - Google Document AI with configurable processors
    - Tesseract OCR with configurable PSM modes
    """
    
    def __init__(self):
        self.google_processors = {
            "OCR_PROCESSOR": "Basic OCR processing",
            "LAYOUT_PARSER_PROCESSOR": "Advanced layout analysis", 
            "FORM_PARSER_PROCESSOR": "Form field extraction",
            "INVOICE_PROCESSOR": "Invoice processing"
        }
        
        self.tesseract_psm_modes = {
            3: "Fully automatic page segmentation",
            6: "Uniform block of text (default)",
            7: "Single text line",
            8: "Single word",
            11: "Sparse text",
            13: "Raw line (no word detection)"
        }
    
    async def process_with_engine(
        self,
        file_data: bytes,
        filename: str,
        engine: str,
        processor: Optional[str] = None,
        psm_mode: Optional[int] = None
    ) -> OCREngineResult:
        """
        Process document with specific OCR engine
        
        Args:
            file_data: Raw file bytes
            filename: Original filename
            engine: "google" or "tesseract"
            processor: Google processor type (if engine="google")
            psm_mode: Tesseract PSM mode (if engine="tesseract")
            
        Returns:
            OCREngineResult with processing results
        """
        
        start_time = time.time()
        
        logger.info(f"Direct OCR routing: {engine} for {filename}")
        
        try:
            if engine.lower() == "google":
                return await self._process_with_google(file_data, filename, processor)
            elif engine.lower() == "tesseract":
                return await self._process_with_tesseract(file_data, filename, psm_mode)
            else:
                return OCREngineResult(
                    success=False,
                    text="",
                    confidence=0.0,
                    processor_used="none",
                    processing_time=time.time() - start_time,
                    engine_used=engine,
                    error_message=f"Unknown OCR engine: {engine}"
                )
                
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"❌ OCR engine routing failed: {str(e)}")
            
            return OCREngineResult(
                success=False,
                text="",
                confidence=0.0,
                processor_used="error",
                processing_time=processing_time,
                engine_used=engine,
                error_message=str(e)
            )
    
    async def _process_with_google(
        self,
        file_data: bytes,
        filename: str,
        processor: Optional[str] = None
    ) -> OCREngineResult:
        """Process with Google Document AI"""
        
        start_time = time.time()
        
        # Default processor if not specified
        if not processor:
            processor = "OCR_PROCESSOR"
        
        # Validate processor
        if processor not in self.google_processors:
            return OCREngineResult(
                success=False,
                text="",
                confidence=0.0,
                processor_used=processor,
                processing_time=time.time() - start_time,
                engine_used="google",
                error_message=f"Invalid Google processor: {processor}"
            )
        
        logger.info(f"Processing with Google Document AI: {processor}")
        
        try:
            # Create simple processing strategy
            from .intelligent_router import ProcessingStrategy
            
            strategy = ProcessingStrategy(
                primary_processor=processor,
                enhancement_processors=[],
                preprocessing_steps=[],
                confidence_threshold=0.8,
                fallback_strategy=[],
                parallel_processing=False,
                batch_processing=False
            )
            
            # Process with Google Document AI
            result = await google_ai_hub.process_document(file_data, strategy, filename)
            
            processing_time = time.time() - start_time
            
            return OCREngineResult(
                success=result.success,
                text=result.text,
                confidence=result.confidence,
                processor_used=result.processor_used,
                processing_time=processing_time,
                engine_used="google",
                error_message=result.error_message if not result.success else None
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"❌ Google Document AI processing failed: {str(e)}")
            
            return OCREngineResult(
                success=False,
                text="",
                confidence=0.0,
                processor_used=processor,
                processing_time=processing_time,
                engine_used="google",
                error_message=str(e)
            )
    
    async def _process_with_tesseract(
        self,
        file_data: bytes,
        filename: str,
        psm_mode: Optional[int] = None
    ) -> OCREngineResult:
        """Process with Tesseract OCR"""
        
        start_time = time.time()
        
        # Default PSM mode if not specified
        if psm_mode is None:
            psm_mode = 6  # Uniform block of text
        
        # Validate PSM mode
        if psm_mode not in self.tesseract_psm_modes:
            return OCREngineResult(
                success=False,
                text="",
                confidence=0.0,
                processor_used=f"tesseract_psm_{psm_mode}",
                processing_time=time.time() - start_time,
                engine_used="tesseract",
                error_message=f"Invalid Tesseract PSM mode: {psm_mode}"
            )
        
        logger.info(f"🔧 Processing with Tesseract OCR: PSM {psm_mode}")
        
        try:
            import pytesseract
            from PIL import Image
            import io
            
            # Open image
            image = Image.open(io.BytesIO(file_data))
            
            # Configure Tesseract
            config = f'--psm {psm_mode}'
            
            # Perform OCR
            extracted_text = pytesseract.image_to_string(image, config=config)
            
            processing_time = time.time() - start_time
            
            # Calculate confidence (Tesseract doesn't provide confidence directly)
            confidence = 0.8 if extracted_text.strip() else 0.0
            
            logger.info(f"✅ Tesseract OCR completed: {len(extracted_text)} chars in {processing_time:.3f}s")
            
            return OCREngineResult(
                success=bool(extracted_text.strip()),
                text=extracted_text.strip(),
                confidence=confidence,
                processor_used=f"tesseract_psm_{psm_mode}",
                processing_time=processing_time,
                engine_used="tesseract"
            )
            
        except ImportError:
            processing_time = time.time() - start_time
            error_msg = "Tesseract not installed. Install with: pip install pytesseract"
            logger.error(f"❌ {error_msg}")
            
            return OCREngineResult(
                success=False,
                text="",
                confidence=0.0,
                processor_used=f"tesseract_psm_{psm_mode}",
                processing_time=processing_time,
                engine_used="tesseract",
                error_message=error_msg
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"❌ Tesseract OCR processing failed: {str(e)}")
            
            return OCREngineResult(
                success=False,
                text="",
                confidence=0.0,
                processor_used=f"tesseract_psm_{psm_mode}",
                processing_time=processing_time,
                engine_used="tesseract",
                error_message=str(e)
            )
    
    def get_available_engines(self) -> Dict[str, Any]:
        """Get information about available OCR engines"""
        
        return {
            "google": {
                "available": google_ai_hub.client is not None,
                "processors": self.google_processors,
                "default_processor": "OCR_PROCESSOR"
            },
            "tesseract": {
                "available": self._check_tesseract_available(),
                "psm_modes": self.tesseract_psm_modes,
                "default_psm": 6
            }
        }
    
    def _check_tesseract_available(self) -> bool:
        """Check if Tesseract is available"""
        try:
            import pytesseract
            return True
        except ImportError:
            return False

# Global OCR engine router instance
ocr_engine_router = OCREngineRouter()
