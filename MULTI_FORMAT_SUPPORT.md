# Multi-Format Document Processing Support

## Overview

The Zurich OCR Engine now supports comprehensive document processing for multiple file formats, providing both direct text extraction and OCR processing as needed. The system intelligently routes documents to the appropriate processing method based on file type and content.

## Supported File Formats

### 1. Image Formats (OCR Processing)
- **PNG, JPG, JPEG, GIF, BMP, TIFF, WEBP**
- Processing: Computer vision preprocessing + OCR engines
- Use cases: Scanned documents, photos of documents, screenshots

### 2. PDF Documents (Hybrid Processing)
- **PDF**
- Processing: Text extraction first, OCR fallback for image-based pages
- Use cases: Forms, reports, policies, mixed text/image documents
- Libraries: `pdfplumber`, `PyPDF2`, `pdf2image`

### 3. Office Documents (Direct Text Extraction)
- **DOCX** - Word documents
- **XLSX** - Excel spreadsheets (with structured data extraction)
- **PPTX/PPTM** - PowerPoint presentations
- Processing: Native text extraction, no OCR needed
- Libraries: `python-docx`, `openpyxl`, `python-pptx`

### 4. Email and Web Content
- **MSG** - Outlook email files
- **HTML/HTM** - Web pages and email templates
- Processing: Content parsing and text extraction
- Libraries: `extract-msg`, `beautifulsoup4`

### 5. Structured Data Formats
- **CSV** - Comma-separated values
- **TXT** - Plain text files
- Processing: Direct text extraction with structured data preservation
- Libraries: Built-in Python `csv` module

### 6. Archive Formats
- **ZIP** - Compressed archives
- Processing: Extract contents and process each file individually
- Supports nested processing of supported formats within archives

### 7. Business Intelligence
- **PBIX** - PowerBI files
- Processing: Metadata extraction from PowerBI file structure
- Use cases: Business report analysis, dashboard content extraction

## Processing Flow

### 1. File Type Detection
```python
# Magic number detection + file extension fallback
file_type = detect_file_type(file_data, filename)
```

### 2. Route to Appropriate Processor
- **Images** → OCR Pipeline (CV preprocessing + OCR engines)
- **Documents** → Direct text extraction
- **Archives** → Extract and process contents
- **Structured data** → Parse and extract with metadata

### 3. Response Format
All formats return a unified response structure:
```json
{
  "success": true,
  "extracted_text": "...",
  "structured_data": {...},  // For CSV, XLSX
  "metadata": {
    "file_type": "docx",
    "extraction_method": "direct",
    "processing_time_ms": 150,
    "pages": 1
  }
}
```

## API Usage Examples

### Text File Processing
```bash
curl -X POST "http://localhost:8000/api/v1/extract-text" \
  -F "file=@document.txt" \
  -F "config={\"vision_analysis\": {\"enabled\": false}}"
```

### Excel Spreadsheet Processing
```bash
curl -X POST "http://localhost:8000/api/v1/extract-text" \
  -F "file=@spreadsheet.xlsx" \
  -F "config={\"output\": {\"structured_extraction\": true}}"
```

### PDF Document Processing
```bash
curl -X POST "http://localhost:8000/api/v1/extract-text" \
  -F "file=@document.pdf" \
  -F "config={\"preprocessing\": {\"enabled\": true}}"
```

### Archive Processing
```bash
curl -X POST "http://localhost:8000/api/v1/extract-text" \
  -F "file=@documents.zip" \
  -F "config={\"debug\": {\"enabled\": true}}"
```

## Configuration Options

### Document-Specific Settings
```json
{
  "preprocessing": {
    "enabled": true,  // For image-based content
    "deskew": true,
    "denoise": true
  },
  "ocr_strategy": {
    "engine": "tesseract",  // For OCR processing
    "confidence_threshold": 0.7
  },
  "output": {
    "structured_extraction": true,  // For CSV, XLSX
    "include_regions": false
  }
}
```

## Performance Characteristics

### Direct Text Extraction (Fast)
- **TXT, CSV, HTML**: < 50ms
- **DOCX, XLSX, PPTX**: 100-500ms
- **PDF (text-based)**: 200-1000ms

### OCR Processing (Slower)
- **Images**: 1-5 seconds
- **PDF (image-based)**: 2-10 seconds per page

### Archive Processing (Variable)
- Depends on contents and number of files
- Processes each file according to its type

## Error Handling

### Unsupported Formats
```json
{
  "success": false,
  "error": "Unsupported file format",
  "supported_formats": {
    "image": [".png", ".jpg", ...],
    "document": [".pdf", ".docx", ...]
  }
}
```

### Corrupted Files
- Graceful degradation to alternative processing methods
- Detailed error reporting in debug mode

## Dependencies

### Core Libraries
```
# Document processing
PyPDF2==3.0.1
pdfplumber==0.10.3
pdf2image==1.16.3
python-docx==1.1.0
openpyxl==3.1.2
pandas==2.1.4
python-pptx==0.6.23
extract-msg==0.45.0
beautifulsoup4==4.12.2

# System dependencies
poppler-utils  # For PDF to image conversion
```

### Installation
```bash
# Install Python dependencies
pip install -r requirements.txt

# Install system dependencies (Ubuntu/Debian)
apt-get install poppler-utils

# Docker build includes all dependencies
docker-compose build ocr-engine
```

## Testing

### Run Multi-Format Tests
```bash
# Execute comprehensive test suite
./test_multi_format.sh

# Test specific format
curl -X POST "http://localhost:8000/api/v1/extract-text" \
  -F "file=@test_documents/sample.csv"
```

### Test Files Included
- `test_documents/sample.txt` - Plain text
- `test_documents/sample.csv` - Structured data
- `test_documents/sample.html` - Web content
- Add your own files for comprehensive testing

## Future Enhancements

### Planned Additions
- **RTF** - Rich Text Format
- **ODT** - OpenDocument Text
- **EML** - Email message format
- **JSON** - Structured data format

### Performance Optimizations
- Parallel processing for archive contents
- Caching for repeated file processing
- Streaming for large files

## Troubleshooting

### Common Issues
1. **Missing dependencies**: Ensure all libraries are installed
2. **File size limits**: Check FastAPI upload limits
3. **Memory usage**: Large files may require increased memory allocation
4. **Encoding issues**: UTF-8 encoding assumed for text files

### Debug Mode
Enable debug mode for detailed processing information:
```json
{
  "debug": {
    "enabled": true,
    "save_intermediate_images": true
  }
}
```
