# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Google Cloud Configuration
GOOGLE_PROJECT_ID=your_google_project_id
GOOGLE_LOCATION=us

# Google Document AI Processor IDs (from your created processors)
GO<PERSON><PERSON><PERSON>_OCR_PROCESSOR_ID=your_ocr_processor_id
GOOGLE_FORM_PARSER_PROCESSOR_ID=your_form_parser_processor_id
GOOGLE_LAYOUT_PROCESSOR_ID=your_layout_processor_id
GOOGLE_BANK_PROCESSOR_ID=your_bank_processor_id
GOOGLE_EXPENSE_PROCESSOR_ID=your_expense_processor_id

# Option 1: File path to credentials JSON
#GOOGLE_APPLICATION_CREDENTIALS=path_to_google_credentials.json
# Option 2: Direct JSON content (preferred for Docker/containers)
GOOGLE_CREDENTIALS_JSON={"type":"service_account","project_id":"your-project-id","private_key_id":"your-private-key-id","private_key":"-----<PERSON><PERSON>IN PRIVATE KEY-----\nyour-private-key\n-----END PRIVATE KEY-----\n","client_email":"*******","client_id":"your-client-id","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/your-service-account%40your-project.iam.gserviceaccount.com","universe_domain":"googleapis.com"}

# LLM Intelligent Routing Settings
LLM_ROUTING_ENABLED=true
LLM_MODEL=gpt-4o
LLM_CONFIDENCE_THRESHOLD=0.8
LLM_MAX_TOKENS=4000
LLM_TEMPERATURE=0.1

# GPT Post-Processing Settings
GPT_POSTPROCESSING_ENABLED=true
GPT_POSTPROCESSING_MODEL=gpt-4o
GPT_POSTPROCESSING_TEMPERATURE=0.2
GPT_POSTPROCESSING_MAX_TOKENS=4000
GPT_VISION_MODEL=gpt-4o

# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1

# Azure Configuration
AZURE_FORM_RECOGNIZER_ENDPOINT=https://your-resource.cognitiveservices.azure.com/
AZURE_FORM_RECOGNIZER_KEY=your_azure_key

# Debug Mode Settings
DEBUG_MODE=false
DEBUG_OUTPUT_DIR=./debug_outputs
SAVE_INTERMEDIATE_IMAGES=false
SAVE_API_RESPONSES=false
DETAILED_TIMING=false
COLLECT_ALL_OUTPUTS=false

# Performance Settings
MAX_FILE_SIZE=********
DEFAULT_OCR_ENGINE=google_document_ai
DEFAULT_CONFIDENCE_THRESHOLD=0.85
ENABLE_FALLBACKS=true
PARALLEL_PROCESSING=true
MAX_WORKERS=8
VISION_COST_THRESHOLD=0.005
BATCH_SIZE=10
REQUEST_TIMEOUT=300

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=./logs/ocr_engine.log

# Security
RATE_LIMIT_PER_MINUTE=60
ENABLE_CORS=true

# Cache Settings
ENABLE_CACHE=true
CACHE_TTL=3600
REDIS_URL=redis://localhost:6379

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090

# Cost Tracking
TRACK_COSTS=true
COST_ALERT_THRESHOLD=10.0
