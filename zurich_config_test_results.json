[{"success": true, "config": "V1_Accuracy", "use_case": "01- <PERSON><PERSON><PERSON>- Travel- Canada", "filename": "Straight-through-processing for high-volume claims.pdf", "processing_time": 0.05872392654418945, "total_time": 0.06779718399047852, "confidence": 0.0, "text_length": 0, "processor_used": "error", "step_timings": null, "error": null}, {"success": true, "config": "V2_Speed", "use_case": "01- <PERSON><PERSON><PERSON>- Travel- Canada", "filename": "Straight-through-processing for high-volume claims.pdf", "processing_time": 0.08997511863708496, "total_time": 0.09269189834594727, "confidence": 0.0, "text_length": 0, "processor_used": "error", "step_timings": null, "error": null}, {"success": true, "config": "V1_Accuracy", "use_case": "01- <PERSON><PERSON><PERSON>- Travel- Canada", "filename": "Redacted_4GRCW2082281_240127-113802 Policy.pdf", "processing_time": 0.03858017921447754, "total_time": 0.04271578788757324, "confidence": 0.0, "text_length": 0, "processor_used": "error", "step_timings": null, "error": null}, {"success": true, "config": "V2_Speed", "use_case": "01- <PERSON><PERSON><PERSON>- Travel- Canada", "filename": "Redacted_4GRCW2082281_240127-113802 Policy.pdf", "processing_time": 0.0389101505279541, "total_time": 0.04180717468261719, "confidence": 0.0, "text_length": 0, "processor_used": "error", "step_timings": null, "error": null}, {"success": true, "config": "V1_Accuracy", "use_case": "01- <PERSON><PERSON><PERSON>- Travel- Canada", "filename": "Redacted_4GRCW2082281_240127-113802.pdf", "processing_time": 3.2216427326202393, "total_time": 3.232091188430786, "confidence": 0.0, "text_length": 0, "processor_used": "error", "step_timings": null, "error": null}, {"success": true, "config": "V2_Speed", "use_case": "01- <PERSON><PERSON><PERSON>- Travel- Canada", "filename": "Redacted_4GRCW2082281_240127-113802.pdf", "processing_time": 3.181196689605713, "total_time": 3.1969799995422363, "confidence": 0.0, "text_length": 0, "processor_used": "error", "step_timings": null, "error": null}, {"success": true, "config": "V1_Accuracy", "use_case": "05- Claims- Liability Decisions- Canada", "filename": "Liability decisions in complex claims.pdf", "processing_time": 0.04907393455505371, "total_time": 0.05213308334350586, "confidence": 0.0, "text_length": 0, "processor_used": "error", "step_timings": null, "error": null}, {"success": true, "config": "V2_Speed", "use_case": "05- Claims- Liability Decisions- Canada", "filename": "Liability decisions in complex claims.pdf", "processing_time": 0.049204111099243164, "total_time": 0.052069902420043945, "confidence": 0.0, "text_length": 0, "processor_used": "error", "step_timings": null, "error": null}, {"success": true, "config": "V1_Accuracy", "use_case": "05- Claims- Liability Decisions- Canada", "filename": "Store+incident+report.tiff.pdf", "processing_time": 22.***************, "total_time": 22.**************, "confidence": 0.9, "text_length": 2117, "processor_used": "LAYOUT_PARSER_PROCESSOR_+_gpt_postprocessing", "step_timings": {"1_format_detection_and_extraction": 1.***************, "1.5_preprocessing": 0.*****************, "2_llm_routing_analysis": 2378.*************, "2.5_preprocessing_update": 0.0, "3_ocr_processing": 10812.************, "4_gpt_postprocessing": 9317.***********}, "error": null}, {"success": true, "config": "V2_Speed", "use_case": "05- Claims- Liability Decisions- Canada", "filename": "Store+incident+report.tiff.pdf", "processing_time": 15.***************, "total_time": 15.**************, "confidence": 0.9, "text_length": 1228, "processor_used": "LAYOUT_PARSER_PROCESSOR_+_gpt_postprocessing", "step_timings": {"1_format_detection_and_extraction": 11.***************, "1.5_preprocessing": 0.****************, "2_llm_routing_analysis": 0.*****************, "2.5_preprocessing_update": 0.0, "3_ocr_processing": 7771.************, "4_gpt_postprocessing": 7396.************}, "error": null}, {"success": true, "config": "V1_Accuracy", "use_case": "05- Claims- Liability Decisions- Canada", "filename": "accident report.tif.pdf", "processing_time": 24.**************, "total_time": 24.***************, "confidence": 0.9, "text_length": 2173, "processor_used": "OCR_PROCESSOR_+_gpt_postprocessing", "step_timings": {"1_format_detection_and_extraction": 3.****************, "1.5_preprocessing": 0.*****************, "2_llm_routing_analysis": 4178.************, "2.5_preprocessing_update": 0.0, "3_ocr_processing": 7399.************, "4_gpt_postprocessing": 13394.************}, "error": null}, {"success": true, "config": "V2_Speed", "use_case": "05- Claims- Liability Decisions- Canada", "filename": "accident report.tif.pdf", "processing_time": 12.***************, "total_time": 12.**************, "confidence": 0.9, "text_length": 1314, "processor_used": "OCR_PROCESSOR_+_gpt_postprocessing", "step_timings": {"1_format_detection_and_extraction": 6.***************, "1.5_preprocessing": 0.****************, "2_llm_routing_analysis": 0.*****************, "2.5_preprocessing_update": 0.0, "3_ocr_processing": 7057.************, "4_gpt_postprocessing": 5814.************}, "error": null}, {"success": true, "config": "V1_Accuracy", "use_case": "03- <PERSON><PERSON>ms- Disability- Ecuador", "filename": "Determination of disability percentage-manual.pdf", "processing_time": 17.***************, "total_time": 17.**************, "confidence": 0.0, "text_length": 0, "processor_used": "error", "step_timings": null, "error": null}, {"success": true, "config": "V2_Speed", "use_case": "03- <PERSON><PERSON>ms- Disability- Ecuador", "filename": "Determination of disability percentage-manual.pdf", "processing_time": 18.**************, "total_time": 18.***************, "confidence": 0.0, "text_length": 0, "processor_used": "error", "step_timings": null, "error": null}, {"success": true, "config": "V1_Accuracy", "use_case": "03- <PERSON><PERSON>ms- Disability- Ecuador", "filename": "Automating Total & Permanent Disability Claims Screening-  Accelerate Coverage Decisions Using AI & Policy Rules.pdf", "processing_time": 0.3910698890686035, "total_time": 0.3971529006958008, "confidence": 0.0, "text_length": 0, "processor_used": "error", "step_timings": null, "error": null}, {"success": true, "config": "V2_Speed", "use_case": "03- <PERSON><PERSON>ms- Disability- Ecuador", "filename": "Automating Total & Permanent Disability Claims Screening-  Accelerate Coverage Decisions Using AI & Policy Rules.pdf", "processing_time": 0.387775182723999, "total_time": 0.3911287784576416, "confidence": 0.0, "text_length": 0, "processor_used": "error", "step_timings": null, "error": null}, {"success": true, "config": "V1_Accuracy", "use_case": "03- <PERSON><PERSON>ms- Disability- Ecuador", "filename": "Certificado de discapacidad.pdf", "processing_time": 21.**************, "total_time": 21.***************, "confidence": 0.9, "text_length": 1975, "processor_used": "LAYOUT_PARSER_PROCESSOR_+_gpt_postprocessing", "step_timings": {"1_format_detection_and_extraction": 12.***************, "1.5_preprocessing": 0.*****************, "2_llm_routing_analysis": 3041.************, "2.5_preprocessing_update": 0.0, "3_ocr_processing": 8124.************, "4_gpt_postprocessing": 10308.************}, "error": null}, {"success": true, "config": "V2_Speed", "use_case": "03- <PERSON><PERSON>ms- Disability- Ecuador", "filename": "Certificado de discapacidad.pdf", "processing_time": 14.**************, "total_time": 14.*************, "confidence": 0.9, "text_length": 1341, "processor_used": "LAYOUT_PARSER_PROCESSOR_+_gpt_postprocessing", "step_timings": {"1_format_detection_and_extraction": 20.***************, "1.5_preprocessing": 0.****************, "2_llm_routing_analysis": 0.*****************, "2.5_preprocessing_update": 0.0, "3_ocr_processing": 7725.************, "4_gpt_postprocessing": 7054.************}, "error": null}, {"success": true, "config": "V1_Accuracy", "use_case": "04- <PERSON><PERSON><PERSON>- Motor- Ecuador", "filename": "Automated Vehicle Claims Processing- AI-Powered Verification &  Fraud Detection for Faster Payouts.pdf", "processing_time": 0.****************, "total_time": 0.***************, "confidence": 0.0, "text_length": 0, "processor_used": "error", "step_timings": null, "error": null}, {"success": true, "config": "V2_Speed", "use_case": "04- <PERSON><PERSON><PERSON>- Motor- Ecuador", "filename": "Automated Vehicle Claims Processing- AI-Powered Verification &  Fraud Detection for Faster Payouts.pdf", "processing_time": 0.****************, "total_time": 0.****************, "confidence": 0.0, "text_length": 0, "processor_used": "error", "step_timings": null, "error": null}, {"success": true, "config": "V1_Accuracy", "use_case": "02- Claims- Motor Liability- UK", "filename": "Agentic AI for Split Liability Analysis in Motor Insurance Claims.pdf", "processing_time": 0.053273916244506836, "total_time": 0.0570371150970459, "confidence": 0.0, "text_length": 0, "processor_used": "error", "step_timings": null, "error": null}, {"success": true, "config": "V2_Speed", "use_case": "02- Claims- Motor Liability- UK", "filename": "Agentic AI for Split Liability Analysis in Motor Insurance Claims.pdf", "processing_time": 0.052845001220703125, "total_time": 0.05565905570983887, "confidence": 0.0, "text_length": 0, "processor_used": "error", "step_timings": null, "error": null}, {"success": true, "config": "V1_Accuracy", "use_case": "02- Claims- Motor Liability- UK", "filename": "150- Redacted ExampleForm.pdf", "processing_time": 0.09752416610717773, "total_time": 0.10110306739807129, "confidence": 0.0, "text_length": 0, "processor_used": "error", "step_timings": null, "error": null}, {"success": true, "config": "V2_Speed", "use_case": "02- Claims- Motor Liability- UK", "filename": "150- Redacted ExampleForm.pdf", "processing_time": 0.09801697731018066, "total_time": 0.10039520263671875, "confidence": 0.0, "text_length": 0, "processor_used": "error", "step_timings": null, "error": null}, {"success": true, "config": "V1_Accuracy", "use_case": "02- Claims- Motor Liability- UK", "filename": "11 -  20-50-719199 - PH ARF.pdf", "processing_time": 25.***************, "total_time": 25.**************, "confidence": 0.9, "text_length": 2026, "processor_used": "OCR_PROCESSOR_+_gpt_postprocessing", "step_timings": {"1_format_detection_and_extraction": 8.***************, "1.5_preprocessing": 0.*****************, "2_llm_routing_analysis": 3158.*************, "2.5_preprocessing_update": 0.0, "3_ocr_processing": 7808.*************, "4_gpt_postprocessing": 14700.************}, "error": null}, {"success": true, "config": "V2_Speed", "use_case": "02- Claims- Motor Liability- UK", "filename": "11 -  20-50-719199 - PH ARF.pdf", "processing_time": 11.**************, "total_time": 11.***************, "confidence": 0.9, "text_length": 1243, "processor_used": "OCR_PROCESSOR_+_gpt_postprocessing", "step_timings": {"1_format_detection_and_extraction": 15.***************, "1.5_preprocessing": 0.*****************, "2_llm_routing_analysis": 0.***************, "2.5_preprocessing_update": 0.0, "3_ocr_processing": 7364.************, "4_gpt_postprocessing": 4509.************}, "error": null}]