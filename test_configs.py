#!/usr/bin/env python3
"""
Simple script to test V1 vs V2 configs on Zurich Challenge data
"""

import os
import json
import requests
import time
from pathlib import Path

# Test configurations
CONFIG_V1 = {"llm_routing_enabled": True, "post_processing": "v1", "preprocessing": "auto", "debug_mode": True}
CONFIG_V2 = {"llm_routing_enabled": True, "post_processing": "v2", "preprocessing": "auto", "debug_mode": True}

API_URL = "http://localhost:8000/api/v1/extract-text"

# Target use cases
USE_CASES = [
    "01- Claims- Travel- Canada",
    "05- Claims- Liability Decisions- Canada", 
    "03- Claims- Disability- Ecuador",
    "04- Claims- Motor- Ecuador",
    "02- Claims- Motor Liability- UK"
]

def find_test_files():
    """Find test files in Zurich Challenge folders"""
    base_path = Path("/Users/<USER>/Development/zurich-ocr-engine/Zurich Challenge")
    test_files = []
    
    for use_case in USE_CASES:
        case_path = base_path / use_case
        if case_path.exists():
            # Find first 3 files (PDF, PNG, JPG) in each use case
            files = []
            for ext in ['*.pdf', '*.png', '*.jpg', '*.jpeg']:
                files.extend(list(case_path.rglob(ext)))
            
            # Take first 3 files per use case
            for file_path in files[:3]:
                test_files.append({
                    'use_case': use_case,
                    'file_path': str(file_path),
                    'filename': file_path.name
                })
    
    return test_files

def test_file(file_info, config, config_name):
    """Test a single file with given config"""
    print(f"Testing {file_info['filename']} with {config_name}")
    
    try:
        with open(file_info['file_path'], 'rb') as f:
            files = {'file': f}
            data = {'config': json.dumps(config)}
            
            start_time = time.time()
            response = requests.post(API_URL, files=files, data=data, timeout=60)
            total_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'config': config_name,
                    'use_case': file_info['use_case'],
                    'filename': file_info['filename'],
                    'processing_time': result.get('processing_time_ms', 0) / 1000,
                    'total_time': total_time,
                    'confidence': result.get('confidence', 0),
                    'text_length': len(result.get('extracted_text', '')),
                    'processor_used': result.get('processor_used', ''),
                    'step_timings': result.get('step_timings', {}),
                    'error': None
                }
            else:
                return {
                    'success': False,
                    'config': config_name,
                    'use_case': file_info['use_case'],
                    'filename': file_info['filename'],
                    'error': f"HTTP {response.status_code}: {response.text[:200]}"
                }
                
    except Exception as e:
        return {
            'success': False,
            'config': config_name,
            'use_case': file_info['use_case'],
            'filename': file_info['filename'],
            'error': str(e)
        }

def main():
    """Run the test"""
    print("Starting Zurich Challenge Config Comparison Test")
    print("=" * 60)

    # Find test files
    test_files = find_test_files()
    print(f"Found {len(test_files)} test files across {len(USE_CASES)} use cases")
    
    results = []
    
    # Test each file with both configs
    for file_info in test_files:
        print(f"\nTesting: {file_info['use_case']} / {file_info['filename']}")

        # Test with V1 config
        result_v1 = test_file(file_info, CONFIG_V1, "V1_Accuracy")
        results.append(result_v1)

        if result_v1['success']:
            print(f"  V1: {result_v1['processing_time']:.1f}s, confidence: {result_v1['confidence']}")
        else:
            print(f"  V1 FAILED: {result_v1['error']}")

        # Test with V2 config
        result_v2 = test_file(file_info, CONFIG_V2, "V2_Speed")
        results.append(result_v2)

        if result_v2['success']:
            print(f"  V2: {result_v2['processing_time']:.1f}s, confidence: {result_v2['confidence']}")
        else:
            print(f"  V2 FAILED: {result_v2['error']}")

        # Quick comparison
        if result_v1['success'] and result_v2['success']:
            speed_improvement = ((result_v1['processing_time'] - result_v2['processing_time']) / result_v1['processing_time']) * 100
            print(f"  V2 is {speed_improvement:.1f}% faster")
    
    # Save results
    output_file = "zurich_config_test_results.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)

    print(f"\nResults saved to: {output_file}")

    # Quick summary
    v1_results = [r for r in results if r['config'] == 'V1_Accuracy' and r['success']]
    v2_results = [r for r in results if r['config'] == 'V2_Speed' and r['success']]

    if v1_results and v2_results:
        avg_v1_time = sum(r['processing_time'] for r in v1_results) / len(v1_results)
        avg_v2_time = sum(r['processing_time'] for r in v2_results) / len(v2_results)
        avg_v1_confidence = sum(r['confidence'] for r in v1_results) / len(v1_results)
        avg_v2_confidence = sum(r['confidence'] for r in v2_results) / len(v2_results)

        print("\nSUMMARY:")
        print("=" * 60)
        print(f"V1 (Accuracy): {avg_v1_time:.1f}s avg, {avg_v1_confidence:.3f} confidence")
        print(f"V2 (Speed):    {avg_v2_time:.1f}s avg, {avg_v2_confidence:.3f} confidence")
        print(f"Speed improvement: {((avg_v1_time - avg_v2_time) / avg_v1_time) * 100:.1f}%")

    print("Test completed!")

if __name__ == "__main__":
    main()
